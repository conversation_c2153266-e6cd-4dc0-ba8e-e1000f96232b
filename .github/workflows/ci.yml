# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# This workflow is meant for as a basic CI run covering the most-important features
# and default Java version. It is meant to run before a PullRequest is merged.

name: "Flink CI (beta)"

on:
  push:
  workflow_dispatch:

permissions: read-all

jobs:
  pre-compile-checks:
    name: "Pre-compile Checks"
    uses: ./.github/workflows/template.pre-compile-checks.yml
  ci:
    name: "Default (Java 17)"
    uses: ./.github/workflows/template.flink-ci.yml
    with:
      environment: 'PROFILE="-Dinclude_hadoop_aws -Djdk17 -Pjava17-target"'
      jdk_version: 17
    secrets:
      s3_bucket: ${{ secrets.IT_CASE_S3_BUCKET }}
      s3_access_key: ${{ secrets.IT_CASE_S3_ACCESS_KEY }}
      s3_secret_key: ${{ secrets.IT_CASE_S3_SECRET_KEY }}
