# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# This workflow is meant as an extended CI run that includes certain features that shall be tested
# and JDK versions that are supported but not considered default.

name: "Nightly trigger (beta)"

on:
  schedule:
    - cron: '0 2 * * *'

jobs:
  Trigger:
    if: github.repository == 'apache/flink'
    permissions:
      actions: write
    strategy:
      matrix:
        branch:
          - master
          - release-2.0
          - release-1.20
          - release-1.19
    runs-on: ubuntu-latest
    steps:
      - name: Trigger Workflow
        uses: actions/github-script@v7
        with:
          script: |
            github.rest.actions.createWorkflowDispatch({
              owner: context.repo.owner,
              repo: context.repo.repo,
              workflow_id: 'nightly.yml',
              ref: '${{ matrix.branch }}'
            })
