# How to contribute to Apache Flink

Thank you for your intention to contribute to the Apache Flink project. As an open-source community, we highly appreciate external contributions to our project.

To make the process smooth for the project *committers* (those who review and accept changes) and *contributors* (those who propose new changes via pull requests), there are a few rules to follow.

## Contribution Guidelines

Please check out the [How to Contribute guide](https://flink.apache.org/contributing/how-to-contribute.html) to understand how contributions are made.
A detailed explanation can be found in our [Contribute Code Guide](https://flink.apache.org/contributing/contribute-code.html) which also contains a list of coding guidelines that you should follow.
For pull requests, there is a [check list](PULL_REQUEST_TEMPLATE.md) with criteria taken from the How to Contribute Guide and the Coding Guidelines.
