# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
#
---
name: "Runs Maven Command"
description: "Executes Flink's Maven wrapper with the passed Maven parameters."
inputs:
  working_directory:
    description: "The directory under which the Maven command should be executed."
    default: "${{ github.workspace }}"
  maven-parameters:
    description: "Any parameters of the Maven command."
    default: ""
  env:
    description: "Any environment-specifics that are meant to be available in the context of the call."
    default: ""
runs:
  using: "composite"
  steps:
    - name: "Runs Maven Command"
      working-directory: "${{ inputs.working_directory }}"
      shell: bash
      run: |
        # errexit needs to be disabled explicitly here because maven-utils.sh handles the error if a mirror is not available
        set +o errexit
        
        ${{ inputs.env }} source "./tools/ci/maven-utils.sh"
        run_mvn ${{ inputs.maven-parameters }}
