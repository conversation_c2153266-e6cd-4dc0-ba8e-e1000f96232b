# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
#
---
name: "Stringify"
description: "Stringifies the given input value."
inputs:
  value:
    description: "Input value to stringify."
    required: true
outputs:
  stringified_value:
    description: "Stringified output value."
    value: ${{ steps.stringify-step.outputs.stringified_value }}
runs:
  using: "composite"
  steps:
    - name: "Stringify '${{ inputs.value }}'"
      id: stringify-step
      shell: bash
      run: |
        # adds a stringified version of the workflow name that can be used for generating unique build artifact names within a composite workflow
        # - replaces any special characters (except for underscores and dots) with dashes
        # - makes the entire string lowercase
        # - condenses multiple dashes into a single one
        # - removes leading and following dashes
        stringified_value=$(echo "${{ inputs.value }}" | tr -C '[:alnum:]._' '-' |  tr '[:upper:]' '[:lower:]' | sed -e 's/--*/-/g' -e 's/^-*//g' -e 's/-*$//g')
        echo "stringified_value=${stringified_value}" >> $GITHUB_OUTPUT
