<table class="configuration table table-bordered">
    <thead>
        <tr>
            <th class="text-left" style="width: 20%">Key</th>
            <th class="text-left" style="width: 15%">Default</th>
            <th class="text-left" style="width: 10%">Type</th>
            <th class="text-left" style="width: 55%">Description</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td><h5>web.access-control-allow-origin</h5></td>
            <td style="word-wrap: break-word;">"*"</td>
            <td>String</td>
            <td>Access-Control-Allow-Origin header for all responses from the web-frontend.</td>
        </tr>
        <tr>
            <td><h5>web.cancel.enable</h5></td>
            <td style="word-wrap: break-word;">true</td>
            <td>Boolean</td>
            <td>Flag indicating whether jobs can be canceled from the web-frontend.</td>
        </tr>
        <tr>
            <td><h5>web.checkpoints.history</h5></td>
            <td style="word-wrap: break-word;">10</td>
            <td>Integer</td>
            <td>Number of checkpoints to remember for recent history.</td>
        </tr>
        <tr>
            <td><h5>web.exception-history-size</h5></td>
            <td style="word-wrap: break-word;">16</td>
            <td>Integer</td>
            <td>The maximum number of failures collected by the exception history per job.</td>
        </tr>
        <tr>
            <td><h5>web.history</h5></td>
            <td style="word-wrap: break-word;">5</td>
            <td>Integer</td>
            <td>Number of archived jobs for the JobManager.</td>
        </tr>
        <tr>
            <td><h5>web.log.path</h5></td>
            <td style="word-wrap: break-word;">(none)</td>
            <td>String</td>
            <td>Path to the log file (may be in /log for standalone but under log directory when using YARN).</td>
        </tr>
        <tr>
            <td><h5>web.refresh-interval</h5></td>
            <td style="word-wrap: break-word;">3 s</td>
            <td>Duration</td>
            <td>Refresh interval for the web-frontend.</td>
        </tr>
        <tr>
            <td><h5>web.rescale.enable</h5></td>
            <td style="word-wrap: break-word;">true</td>
            <td>Boolean</td>
            <td>Flag indicating whether jobs can be rescaled from the web-frontend.</td>
        </tr>
        <tr>
            <td><h5>web.submit.enable</h5></td>
            <td style="word-wrap: break-word;">true</td>
            <td>Boolean</td>
            <td>Flag indicating whether jobs can be uploaded and run from the web-frontend.</td>
        </tr>
        <tr>
            <td><h5>web.timeout</h5></td>
            <td style="word-wrap: break-word;">10 min</td>
            <td>Duration</td>
            <td>Timeout for asynchronous operations by the web monitor.</td>
        </tr>
        <tr>
            <td><h5>web.tmpdir</h5></td>
            <td style="word-wrap: break-word;">System.getProperty("java.io.tmpdir")</td>
            <td>String</td>
            <td>Local directory that is used by the REST API for temporary files.</td>
        </tr>
        <tr>
            <td><h5>web.upload.dir</h5></td>
            <td style="word-wrap: break-word;">(none)</td>
            <td>String</td>
            <td>Local directory that is used by the REST API for storing uploaded jars. If not specified a dynamic directory will be created under <code class="highlighter-rouge">web.tmpdir</code>.</td>
        </tr>
    </tbody>
</table>
