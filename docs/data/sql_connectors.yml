# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
# http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License

# INSTRUCTIONS:
#
# In order to add a new connector/format add a new entry to this file.
# You need specify a name that will be used in e.g. the description of the connector/format and
# a category (either "format" or "connector"). The category determines which table will the entry
# end up in on the Download page. The "maven" parameter describes the name of the maven module. The
# three parameters are required.
#
# If you specify "builtin=true" the corresponding table on the connector/format will not contain
# a link, but just a "Built-in" entry. If the built-in is set to true you do not need to provide the
# sql_url.
#
# If a connector comes with different versions for the external system, you can put those under a
# "versions" property. Each entry in the "versions" section should have a "version", which
# determines name for the version and "maven" and "sql_url" entries for that particular version.
# If you use the "versions" property, "maven" and "sql_url" should not be present in the top level
# section of the connector. (Multiple versions are supported only for the connector for now. If you
# need multiple versions support for formats, please update downloads.md)
#
# NOTE: You can use the variables $scala_version and $version in "sql_url" and "maven" properties.

avro:
    name: Avro
    maven: flink-avro
    category: format
    sql_url: https://repo.maven.apache.org/maven2/org/apache/flink/flink-sql-avro/$version/flink-sql-avro-$version.jar

avro-confluent:
    name: Avro Schema Registry
    maven:
      - flink-avro-confluent-registry
      - flink-avro
    category: format
    sql_url: https://repo.maven.apache.org/maven2/org/apache/flink/flink-sql-avro-confluent-registry/$version/flink-sql-avro-confluent-registry-$version.jar

orc:
    name: ORC
    maven: flink-orc
    category: format
    sql_url: https://repo.maven.apache.org/maven2/org/apache/flink/flink-sql-orc/$version/flink-sql-orc-$version.jar

parquet:
    name: Parquet
    maven: flink-parquet
    category: format
    sql_url: https://repo.maven.apache.org/maven2/org/apache/flink/flink-sql-parquet/$version/flink-sql-parquet-$version.jar

protobuf:
  name: Protobuf
  maven: flink-protobuf
  category: format
  sql_url: https://repo.maven.apache.org/maven2/org/apache/flink/flink-sql-protobuf/$version/flink-sql-protobuf-$version.jar

debezium-avro-confluent:
    name: Debezium
    maven: flink-avro-confluent-registry
    category: format
    sql_url: https://repo.maven.apache.org/maven2/org/apache/flink/flink-sql-avro-confluent-registry/$version/flink-sql-avro-confluent-registry-$version.jar

debezium-json:
    name: Debezium
    maven: flink-json
    category: format
    builtin: true

canal:
    name: Canal
    maven: flink-json
    category: format
    builtin: true

maxwell:
    name: Maxwell
    maven: flink-json
    category: format
    builtin: true

ogg-json:
    name: Ogg
    maven: flink-json
    catagory: format
    builtin: true

csv:
    name: CSV
    maven: flink-csv
    category: format
    builtin: true

json:
    name: Json
    maven: flink-json
    category: format
    builtin: true

raw:
    name: RAW
    maven: flink-raw
    category: format
    builtin: true

files:
  name: Files
  category: connector
  maven: flink-connector-files
  sql_url: https://repo.maven.apache.org/maven2/org/apache/flink/flink-connector-files/$version/flink-connector-files-$version.jar

elastic:
    name: Elasticsearch
    category: connector
    versions:
        - version: 6.x
          maven: flink-connector-elasticsearch6
          sql_url: https://repo.maven.apache.org/maven2/org/apache/flink/flink-sql-connector-elasticsearch6/3.0.0-1.16/flink-sql-connector-elasticsearch6-3.0.0-1.16.jar
        - version: 7.x and later versions
          maven: flink-connector-elasticsearch7
          sql_url: https://repo.maven.apache.org/maven2/org/apache/flink/flink-sql-connector-elasticsearch7/3.0.0-1.16/flink-sql-connector-elasticsearch7-3.0.0-1.16.jar

hbase:
    name: HBase
    category: connector
    versions:
        - version: 1.4.x
          maven: flink-connector-hbase-1.4
          sql_url: https://repo.maven.apache.org/maven2/org/apache/flink/flink-sql-connector-hbase-1.4/$version/flink-sql-connector-hbase-1.4-$version.jar
        - version: 2.2.x
          maven: flink-connector-hbase-2.2
          sql_url: https://repo.maven.apache.org/maven2/org/apache/flink/flink-sql-connector-hbase-2.2/$version/flink-sql-connector-hbase-2.2-$version.jar

jdbc:
    name: JDBC
    category: connector
    maven: flink-connector-jdbc
    sql_url: https://repo.maven.apache.org/maven2/org/apache/flink/flink-connector-jdbc/3.0.0-1.16/flink-connector-jdbc-3.0.0-1.16.jar

kafka:
    name: Kafka
    category: connector
    versions:
        - version: universal
          maven: flink-connector-kafka
          sql_url: https://repo.maven.apache.org/maven2/org/apache/flink/flink-sql-connector-kafka/$version/flink-sql-connector-kafka-$version.jar

upsert-kafka:
    name: Upsert Kafka
    category: connector
    versions:
        - version: universal
          maven: flink-connector-kafka
          sql_url: https://repo.maven.apache.org/maven2/org/apache/flink/flink-sql-connector-kafka/$version/flink-sql-connector-kafka-$version.jar

kinesis:
    name: Amazon Kinesis Data Streams
    category: connector
    versions:
        - versions: universal
          maven: flink-connector-kinesis
          sql_url: https://repo.maven.apache.org/maven2/org/apache/flink/flink-sql-connector-kinesis/4.1.0-1.16/flink-sql-connector-kinesis-4.1.0-1.16.jar

aws-kinesis-firehose:
    name: Amazon Kinesis Data Firehose
    category: connector
    versions:
        - version: universal
          maven: flink-connector-aws-kinesis-firehose
          sql_url: https://repo.maven.apache.org/maven2/org/apache/flink/flink-sql-connector-aws-kinesis-firehose/4.1.0-1.16/flink-sql-connector-aws-kinesis-firehose-4.1.0-1.16.jar

dynamodb:
    name: Amazon DynamoDB
    category: connector
    versions:
        - version: universal
          maven: flink-connector-dynamodb
          sql_url: https://repo.maven.apache.org/maven2/org/apache/flink/flink-sql-connector-dynamodb/4.1.0-1.16/flink-sql-connector-dynamodb-4.1.0-1.16.jar

pulsar:
    name: Pulsar
    category: connector
    maven: flink-connector-pulsar
    sql_url: https://repo.maven.apache.org/maven2/org/apache/flink/flink-sql-connector-pulsar/3.0.0-1.16/flink-sql-connector-pulsar-3.0.0-1.16.jar

rabbitmq:
    name: RabbitMQ
    category: connector
    maven: flink-connector-rabbitmq
    sql_url: https://repo.maven.apache.org/maven2/org/apache/flink/flink-sql-connector-rabbitmq/3.0.0-1.16/flink-sql-connector-rabbitmq-3.0.0-1.16.jar

opensearch:
  name: Opensearch
  category: connector
  versions:
    - version: 1.0.0
      maven: flink-connector-opensearch
      sql_url: https://repo.maven.apache.org/maven2/org/apache/flink/flink-sql-connector-opensearch/1.0.0-1.16/flink-sql-connector-opensearch-1.0.0-1.16.jar
      
mongodb:
    name: MongoDB
    category: connector
    maven: flink-connector-mongodb
    sql_url: https://repo.maven.apache.org/maven2/org/apache/flink/flink-sql-connector-mongodb/1.0.0-1.16/flink-sql-connector-mongodb-1.0.0-1.16.jar
