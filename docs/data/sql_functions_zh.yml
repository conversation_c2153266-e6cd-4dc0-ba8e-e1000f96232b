# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
# http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License

comparison:
  - sql: value1 = value2
    table: value1 === value2
    description: 如果 value1 等于 value2 返回 `TRUE`；如果 value1 或者 value2 为 `NULL` 返回 `UNKNOW`。
  - sql: value1 <> value2
    table: value1 !== value2
    description: 如果 value1 不等于 value2 返回 `TRUE`；如果 value1 或 value2 为 `NULL` 返回 `UNKNOWN`。
  - sql: value1 > value2
    table: value1 > value2
    description: 如果 value1 大于 value2 返回 `TRUE`；如果 value1 或 value2 为 `NULL` 返回 `UNKNOWN`。
  - sql: value1 >= value2
    table: value1 >= value2
    description: 如果 value1 大于或等于 value2 返回 `TRUE`；如果 value1 或 value2 为 `NULL` 返回 `UNKNOWN`。
  - sql: value1 < value2
    table: value1 < value2
    description: 如果 value1 小于 value2 返回 `TRUE`；如果 value1 或 value2 为 `NULL` 返回 `UNKNOWN`。
  - sql: value1 <= value2
    table: value1 <= value2
    description: 如果 value1 小于或等于 value2 返回 `TRUE`；如果 value1 或 value2 为 `NULL` 返回 `UNKNOWN`。
  - sql: value IS NULL
    table: value.isNull
    description: 如果值为 `NULL` 返回 `TRUE`。
  - sql: value IS NOT NULL
    table: value.isNotNull
    description: 如果值不为 `NULL` 返回 `TRUE`。
  - sql: value1 IS DISTINCT FROM value2
    description: |
      A 和 B 的数据类型和值不完全相同返回 `TRUE`。A 和 B 的数据类型和值都相同返回 `FALSE`。`NULL` 视为与任何其他值相同。
      例如 `1 IS DISTINCT FROM NULL` 返回 `TRUE`；`NULL IS DISTINCT FROM NULL` 返回 `FALSE`。
  - sql: value1 IS NOT DISTINCT FROM value2
    description: |
      A 和 B 的数据类型和值都相同返回 `TRUE`。A 和 B 的数据类型和值不完全相同则返回 `FALSE`。`NULL` 视为与任何其他值相同。
      例如 `1 IS NOT DISTINCT FROM NULL` 返回 `FALSE`；`NULL IS NOT DISTINCT FROM NULL` 返回 `TRUE`。
  - sql: value1 BETWEEN [ ASYMMETRIC | SYMMETRIC ] value2 AND value3
    description: |
      默认或使用 `ASYMMETRIC` 关键字的情况下，如果 value1 大于等于 value2 且小于等于 value3 返回 `TRUE`。
      使用 `SYMMETRIC` 关键字则 value1 在 value2 和 value3 之间返回 `TRUE`。
      当 value2 或 value3 为 `NULL` 时，返回 `FALSE` 或 `UNKNOWN`。
      例如 `12 BETWEEN 15 AND 12` 返回 `FALSE`；
      `12 BETWEEN SYMMETRIC 15 AND 12` 返回 `TRUE`；
      `12 BETWEEN 10 AND NULL` 返回 `UNKNOWN`；
      `12 BETWEEN NULL AND 10` 返回 `FALSE`；
      `12 BETWEEN SYMMETRIC NULL AND 12` 返回 `UNKNOWN`。
  - sql: value1 NOT BETWEEN [ ASYMMETRIC | SYMMETRIC ] value2 AND value3
    description: |
      默认或使用 `ASYMMETRIC` 关键字的情况下，如果 value1 小于 value2 或大于 value3，则返回 `TRUE`。
      使用 `SYMMETRIC` 关键字则 value1 不在 value2 和 value3 之间返回 `TRUE`。
      当 value2 或 value3 为 `NULL` 时，返回 `TRUE` 或 `UNKNOWN`。
      例如 `12 NOT BETWEEN 15 AND 12` 返回 `TRUE`；
      `12 NOT BETWEEN SYMMETRIC 15 AND 12` 返回 `FALSE`；
      `12 NOT BETWEEN NULL AND 15` 返回 `UNKNOWN`；
      `12 NOT BETWEEN 15 AND NULL` 返回 `TRUE`；
      `12 NOT BETWEEN SYMMETRIC 12 AND NULL` 返回 `UNKNOWN`。
  - sql: string1 LIKE string2 [ ESCAPE char ]
    table: string1.like(string2[, char])
    description: |
      如果 string1 匹配 string2 返回 `TRUE`；如果 string1 或 string2 为 `NULL` 返回 `UNKNOWN`。
      如果需要可以定义包含单个字符的转义字符，默认为 '\'。
  - sql: string1 NOT LIKE string2 [ ESCAPE char ]
    description: |
      如果 string1 与 string2 不匹配返回 `TRUE`；如果 string1 或 string2 为 `NULL` 返回 `UNKNOWN`。
      如果需要可以定义包含单个字符的转义字符，默认为 '\'。
  - sql: string1 SIMILAR TO string2 [ ESCAPE char ]
    table: string1.similar(string2)
    description: |
      如果 string1 匹配 SQL 正则表达式 string2 返回 `TRUE`；如果 string1 或 string2 为 `NULL` 返回
      `UNKNOWN`。如果需要可以定义转义字符。尚不支持转义字符。
  - sql: string1 NOT SIMILAR TO string2 [ ESCAPE char ]
    description: |
      如果 string1 与 SQL 正则表达式 string2 不匹配返回 `TRUE`；如果 string1 或 string2 为 `NULL` 返回
      `UNKNOWN`。如果需要可以定义转义字符。尚不支持转义字符。
  - sql: value1 IN (value2 [, value3]* )
    table: value1.in(value2)
    description: |
      在给定列表 (value2, value3, ...) 中存在 value1 返回 `TRUE`。当列表包含 `NULL`，如果可以找到 value1 则返回
      `TRUE`，否则返回 `UNKNOWN`。如果 value1 为 `NULL` 则始终返回 `UNKNOWN`。例如 `4 IN (1, 2, 3)` 返回
      `FALSE`；`1 IN (1, 2, NULL)` 返回 `TRUE`；`4 IN (1, 2, NULL)` 返回 `UNKNOWN`。
  - sql: value1 NOT IN (value2 [, value3]* )
    description: |
      在给定列表 (value2, value3, ...) 中不存在 value1 返回 `TRUE`。当列表包含 `NULL`，如果可以找到 value1 则
      返回 `FALSE`，否则返回 `UNKNOWN`。如果 value1 为 `NULL`，则始终返回 `UNKNOWN`。例如 `4 NOT IN (1, 2, 3)`
      返回 `TRUE`；`1 NOT IN (1, 2, NULL)` 返回 `FALSE`；`4 NOT IN (1, 2, NULL)` 返回 `UNKNOWN`。
  - sql: EXISTS (sub-query)
    description: |
      如果子查询至少返回一行则返回 `TRUE`。 仅支持可以在 join 和分组操作中可以被重写的操作。对于流式查询，该操作在 join
      和分组操作中被重写。计算查询结果所需的状态大小取决于输入行的数量，状态可能会无限增长。请在查询配置中合理配置状态保留时间，以防止状态过大。
  - sql: value IN (sub-query)
    table: value1.in(TABLE)
    description: 如果 value 等于子查询结果集中的一行则返回 `TRUE`。
  - sql: value NOT IN (sub-query)
    description: 如果 value 不包含于子查询返回的行则返回 `TRUE`。
  - table: value1.between(value2, value3)
    description: |
      如果 value1 大于或等于 value2 且小于或等于 value3 返回 `TRUE`。当 value2 或 value3 为 `NULL` 时，
      返回 `FALSE` 或 `UNKNOWN`。
  - table: value1.notBetween(value2, value3)
    description: |
      如果 value1 大于或等于 value2 且小于或等于 value3 返回 `FALSE`。当 value2 或 value3 为 `NULL` 时，
      返回 `TRUE` 或 `UNKNOWN`。

logical:
  - sql: boolean1 OR boolean2
    table: BOOLEAN1 || BOOLEAN2
    description: |
      如果 boolean1 为 `TRUE` 或 boolean2 为 `TRUE` 返回 `TRUE`。支持三值逻辑。
      例如 `true || Null(BOOLEAN)` 返回 `TRUE`。
  - sql: boolean1 AND boolean2
    table: BOOLEAN1 && BOOLEAN2
    description: |
      如果 boolean1 和 boolean2 都为 `TRUE` 返回 `TRUE`。支持三值逻辑。
      例如 `true && Null(BOOLEAN)` 返回 `UNKNOWN`。
  - sql: NOT boolean
    table: BOOLEAN.not(), not(BOOLEAN), 或者 '!BOOLEAN' (仅适用于Scala)
    description: |
      如果 boolean 为 `FALSE` 返回 `TRUE`；如果 boolean 为 `TRUE` 返回 `FALSE`；如果 boolean 为 `UNKNOWN`
      返回 `UNKNOWN`。
  - sql: boolean IS FALSE
    table: BOOLEAN.isFalse
    description: 如果 boolean 为 `FALSE` 返回 `TRUE`；如果 boolean 为 `TRUE` 或 `UNKNOWN` 返回 `FALSE`。
  - sql: boolean IS NOT FALSE
    table: BOOLEAN.isNotFalse
    description: 如果 boolean 为 `TRUE` 或 `UNKNOWN` 返回 `TRUE`；如果 boolean 为 `FALSE` 返回 `FALSE`。
  - sql: boolean IS TRUE
    table: BOOLEAN.isTrue
    description: 如果 boolean 为 `TRUE` 返回 `TRUE`；如果 boolean 为 `FALSE` 或 `UNKNOWN` 返回 `FALSE`。
  - sql: boolean IS NOT TRUE
    table: BOOLEAN.isNotTrue
    description: 如果 boolean 为 `FALSE` 或 `UNKNOWN` 返回 `TRUE`；如果 boolean 为 `TRUE` 返回 `FALSE`。
  - sql: boolean IS UNKNOWN
    description: 如果 boolean 为 `UNKNOWN` 返回 `TRUE`；如果 boolean 为 `TRUE` 或 `FALSE` 返回 `FALSE`。
  - sql: boolean IS NOT UNKNOWN
    description: 如果 boolean 为 `TRUE` 或 `FALSE` 返回 `TRUE`；如果 boolean 为 `UNKNOWN` 返回 `FALSE`。

arithmetic:
  - sql: + numeric
    table: + NUMERIC
    description: 返回 numeric。
  - sql: '- numeric'
    table: '- NUMERIC'
    description: 返回 numeric 的相反数。
  - sql: numeric1 + numeric2
    table: NUMERIC1 + NUMERIC2
    description: 返回 numeric1 加 numeric2。
  - sql: numeric1 - numeric2
    table: NUMERIC1 - NUMERIC2
    description: 返回 numeric1 减 numeric2。
  - sql: numeric1 * numberic2
    table: NUMERIC1 * NUMERIC2
    description: 返回 numeric1 乘以 numeric2。
  - sql: numeric1 / numeric2
    table: NUMERIC1 / NUMERIC2
    description: 返回 numeric1 除以 numeric2。
  - sql: numeric1 % numeric2
    table: MOD(NUMERIC1, NUMERIC2)
    description: 返回 numeric1 除以 numeric2 的余数（模数）。仅当 numeric1 为负时，结果才为负。
  - sql: POWER(numeric1, numeric2)
    table: NUMERIC1.power(NUMERIC2)
    description: 返回 numeric1 的 numeric2 次方。
  - sql: ABS(numeric)
    table: NUMERIC.abs()
    description: 返回 numeric 的绝对值。
  - sql: SQRT(numeric)
    table: NUMERIC.sqrt()
    description: 返回 numeric 的平方根。
  - sql: LN(numeric)
    table: NUMERIC.ln()
    description: 返回 numeric 的自然对数（以 e 为底）。
  - sql: LOG10(numeric)
    table: NUMERIC.log10()
    description: 返回以 10 为底的 numeric 的对数。
  - sql: LOG2(numeric)
    table: NUMERIC.log2()
    description: 返回以 2 为底的 numeric 的对数。
  - sql: |
      LOG(numeric2)
      LOG(numeric1, numeric2)
    table: |
      NUMERIC1.log()
      NUMERIC1.log(NUMERIC2)
    description: |
      当用一个参数调用时，返回 numeric2 的自然对数。当使用两个参数调用时，此函数返回 numeric2 以 numeric1
      为底的对数，此时 numeric2 必须大于 0，numeric1 必须大于 1。
  - sql: EXP(numeric)
    table: NUMERIC.exp()
    description: 返回 e 的 numeric 次幂。
  - sql: |
      CEIL(numeric)
      CEILING(numeric)
    table: |
      NUMERIC.ceil()
      NUMERIC.ceiling()
    description: 向上取整，并返回大于或等于 numeric 的最小整数。
  - sql: FLOOR(numeric)
    table: NUMERIC.floor()
    description: 向下取整，并返回小于或等于 numeric 的最大整数。
  - sql: SIN(numeric)
    table: NUMERIC.sin()
    description: 返回 numeric 的正弦值。
  - sql: SINH(numeric)
    table: NUMERIC.sinh()
    description: 返回 numeric 的双曲正弦值。返回类型为 DOUBLE。
  - sql: COS(numeric)
    table: NUMERIC.cos()
    description: 返回 numeric 的余弦值。
  - sql: TAN(numeric)
    table: NUMERIC.tan()
    description: 返回 numeric 的正切值。
  - sql: TANH(numeric)
    table: NUMERIC.tanh()
    description: 返回 numeric 的双曲正切值。返回类型为 DOUBLE。
  - sql: COT(numeric)
    table: NUMERIC.cot()
    description: 返回 numeric 的余切值。
  - sql: ASIN(numeric)
    table: NUMERIC.asin()
    description: 返回 numeric 的反正弦值。
  - sql: ACOS(numeric)
    table: NUMERIC.acos()
    description: 返回 numeric 的反余弦值。
  - sql: ATAN(numeric)
    table: NUMERIC.atan()
    description: 返回 numeric 的反正切值。
  - sql: ATAN2(numeric1, numeric2)
    table: atan2(NUMERIC1, NUMERIC2)
    description: 返回坐标 (numeric1, numeric2) 的反正切。
  - sql: COSH(numeric)
    table: NUMERIC.cosh()
    description: 返回 numeric 的双曲余弦值。返回值类型为 DOUBLE。
  - sql: DEGREES(numeric)
    table: NUMERIC.degrees()
    description: 返回弧度 numeric 的度数表示。
  - sql: RADIANS(numeric)
    table: NUMERIC.radians()
    description: 返回度数 numeric 的弧度表示。
  - sql: SIGN(numeric)
    table: NUMERIC.sign()
    description: 返回 numeric 的符号。
  - sql: ROUND(numeric, INT)
    table: NUMERIC.round(INT)
    description: 返回 numeric 四舍五入保留 INT 小数位的值。
  - sql: PI()
    table: pi()
    description: 返回无比接近 pi 的值。
  - sql: E()
    table: e()
    description: 返回无比接近 e 的值。
  - sql: RAND()
    table: rand()
    description: 返回 [0.0, 1.0) 范围内的伪随机双精度值。
  - sql: RAND(INT)
    table: rand(INT)
    description: |
      返回范围为 [0.0, 1.0) 的伪随机双精度值，初始种子为 INT。
      如果两个 RAND 函数具有相同的初始种子，它们将返回相同的数字序列。
  - sql: RAND_INTEGER(INT)
    table: randInteger(INT)
    description: 返回 [0, INT) 范围内的伪随机整数。
  - sql: RAND_INTEGER(INT1, INT2)
    table: randInteger(INT1, INT2)
    description: |
      返回范围为 [0, INT2) 的伪随机整数，初始种子为 INT1。
      如果两个 RAND_INTGER 函数具有相同的初始种子和边界，它们将返回相同的数字序列。
  - sql: UUID()
    table: uuid()
    description: |
      根据 RFC 4122 类型 4（伪随机生成）UUID，返回 UUID（通用唯一标识符）字符串。
      例如“3d3c68f7-f608-473f-b60c-b0c44ad4cc4e”，UUID 是使用加密强的伪随机数生成器生成的。
  - sql: BIN(INT)
    table: INT.bin()
    description: |
      以二进制格式返回 INTEGER 的字符串表示形式。如果 INTEGER 为 `NULL`，则返回 `NULL`。
      例如 4.bin() 返回“100”，12.bin() 返回“1100”。
  - sql: |
      HEX(numeric)
      HEX(string)
    table: |
      NUMERIC.hex()
      STRING.hex()
    description: |
      以十六进制格式返回整数 numeric 或字符串 string 的字符串表示。如果参数为 `NULL`，则返回 `NULL`。
      例如数字 20 返回“14”，数字 100 返回“64”，字符串“hello,world” 返回“68656C6C6F2C776F726C64”。
  - sql: UNHEX(expr)
    table: expr.unhex()
    description: |
      将十六进制字符串 expr 转换为 BINARY。如果 expr 的长度为奇数，则会舍弃第一个字符，并在结果开头补充一个空字节。
      
      E.g., SELECT DECODE(UNHEX('466C696E6B') , 'UTF-8' ) or '466C696E6B'.unhex().decode('UTF-8') returns "Flink".
      
      expr <CHAR | VARCHAR>
      
      返回一个 BINARY。如果 expr 为 `NULL` 或包含非十六进制字符，则返回 `NULL`。
  - sql: TRUNCATE(numeric1, integer2)
    table: NUMERIC1.truncate(INTEGER2)
    description: |
      返回截取 integer2 位小数的数字。如果 numeric1 或 integer2 为 `NULL`，则返回 `NULL`。
      如果 integer2 为 0，则结果没有小数点或小数部分。integer2 可以为负数，使值的小数点左边的 integer2 位变为零。
      此函数也可以只传入一个参数 numeric1 且不设置参数 integer2 来使用。如果未设置 integer2 则 integer2 默认为 0。
      例如 42.324.truncate(2) 为 42.32，42.324.truncate() 为 42.0。
  - sql: PERCENTILE(expr, percentage[, frequency])
    table: expr.percentile(percentage[, frequency])
    description: |
      返回 expr 的 percentage 百分位值。
      
      E.g., SELECT PERCENTILE(age, 0.25) FROM (VALUES  (10), (20), (30), (40)) AS age or $('age').percentile(0.25) returns 17.5

      percentage 必须是一个在 `[0.0, 1.0]` 之间的字面数值，或者是一个该范围的数组。
      如果传递了一个可变表达式给这个函数，结果将根据其中的任意一个值进行计算。
      frequency 描述了 expr 的统计次数，默认值为 1。

      如果没有 expr 恰好位于指定的百分位上，则结果通过对两个最接近的 expr 进行线性插值来计算。
      如果 expr 或 frequency 为 `NULL`，或者 frequency 不是正数，则将忽略该输入行。

      注意: 建议在窗口场景中使用此函数，因为它通常能提供更好的性能。
      在常规的分组聚合场景中，用户应注意每条记录引发的全面排序带来的性能开销。

      `value <NUMERIC>, percentage [<NUMERIC NOT NULL> | <ARRAY<NUMERIC NOT NULL> NOT NULL>], frequency <INTEGER_NUMERIC>`
      `(INTEGER_NUMERIC: TINYINT, SMALLINT, INTEGER, BIGINT)`
      `(NUMERIC: INTEGER_NUMERIC, FLOAT, DOUBLE, DECIMAL)`

      如果 percentage 是数字，返回类型为 `DOUBLE`；如果 percentage 是数组，返回类型为 `ARRAY<DOUBLE>`。 如果 percentage 是空数组，则返回 `NULL`。

string:
  - sql: string1 || string2
    table: STRING1 + STRING2
    description: 返回 STRING1 和 STRING2 的连接。
  - sql: |
      CHAR_LENGTH(string)
      CHARACTER_LENGTH(string)
    table: STRING.charLength()
    description: 返回字符串中的字符数。
  - sql: UPPER(string)
    table: STRING.upperCase()
    description: 以大写形式返回字符串。
  - sql: LOWER(string)
    table: STRING.lowerCase()
    description: 以小写形式返回字符串。
  - sql: POSITION(string1 IN string2)
    table: STRING1.position(STRING2)
    description: 返回 STRING2 中第一次出现 STRING1 的位置（从 1 开始）；如果在 STRING2 中找不到 STRING1 返回 0。
  - sql: PRINTF(format[, obj]*)
    table: format.printf(obj...)
    description: |
      格式化 print-style 样式的字符串。
      
      函数使用 java.util.Formatter (Locale.US) 解析字符串。
      
      null obj 被格式化为字符串 "null"。
      
      strfmt <CHAR | VARCHAR>, obj <ANY>
      
      返回一个 STRING 格式的格式化后的字符串。如果 format 为 `NULL` 或不合法，则返回 `NULL`。
  - sql: TRIM([ BOTH | LEADING | TRAILING ] string1 FROM string2)
    table: |
      STRING1.trim(LEADING, STRING2)
      STRING1.trim(TRAILING, STRING2)
      STRING1.trim(BOTH, STRING2)
      STRING1.trim(BOTH)
      STRING1.trim()
    description: 删除字符串 STRING1 开头和结尾可能存在的字符串 STRING2 并返回。默认情况下，会删除 STRING1 开头和结尾的空格。
  - sql: LTRIM(str[, trimStr])
    table: str.ltrim([trimStr])
    description: |
      从 str 的开头删除 trimStr 中的字符。trimStr 默认设置为空格。
      
      例如 `' This is a test String.'.ltrim()` 返回 `'This is a test String.'`。
      
      str <CHAR | VARCHAR>, trimStr <CHAR | VARCHAR>
      
      返回一个 STRING 格式的裁剪后的 str。如果任何参数为 `NULL`，则返回 `NULL`。
  - sql: RTRIM(str[, trimStr])
    table: str.rtrim([trimStr])
    description: |
      从 str 的结尾删除 trimStr 中的字符。trimStr 默认设置为空格。
      
      例如 `'This is a test String. '.rtrim()` 返回 `'This is a test String.'`。
      
      str <CHAR | VARCHAR>, trimStr <CHAR | VARCHAR>
      
      返回一个 STRING 格式的裁剪后的 str。如果任何参数为 `NULL`，则返回 `NULL`。
  - sql: BTRIM(str[, trimStr])
    table: str.btrim([trimStr])
    description: |
      从 str 的开头和结尾删除 trimStr 中的字符。trimStr 默认设置为空格。      
      
      E.g., BTRIM('  www.apache.org  ') or '  www.apache.org  '.btrim() returns "www.apache.org", BTRIM('/www.apache.org/', '/') or '  www.apache.org  '.btrim() returns "www.apache.org".
      
      str <CHAR | VARCHAR>, trimStr <CHAR | VARCHAR>
      
      返回一个 STRING 格式的裁剪后的 str。如果任何参数为 `NULL`，则返回 `NULL`。
  - sql: REPEAT(string, int)
    table: STRING.repeat(INT)
    description: |
      返回 INT 个 `string` 连接的字符串。
      例如 `REPEAT('This is a test String.', 2)` 返回 `"This is a test String.This is a test String."`。
  - sql: REGEXP_REPLACE(string1, string2, string3)
    table: STRING1.regexpReplace(STRING2, STRING3)
    description: |
      返回 STRING1 所有与正则表达式 STRING2 匹配的子字符串被 STRING3 替换后的字符串。
      例如 `'foobar'.regexpReplace('oo|ar', '')` 返回 `"fb"`。
  - sql: OVERLAY(string1 PLACING string2 FROM integer1 [ FOR integer2 ])
    table: |
      STRING1.overlay(STRING2, INT1)
      STRING1.overlay(STRING2, INT1, INT2)
    description: |
      将字符串 STRING1 从位置 INT1 开始替换 INT2（默认为 STRING2 的长度）个来自字符串 STRING2 的字符并返回。
      例如 `'xxxxxtest'.overlay('xxxx', 6)` 返回 `"xxxxxxxxx"`；
      `'xxxxxtest'.overlay('xxxx', 6, 2)` 返回 `"xxxxxxxxxst"`。
  - sql: STARTSWITH(expr, startExpr)
    table: expr.startsWith(startExpr)
    description: |
      判断 expr 是否以 startExpr 开头。如果 startExpr 为空，则结果为 true。
      
      expr 和 startExpr 应具有相同的类型。
      
      `expr <CHAR | VARCHAR>, startExpr <CHAR | VARCHAR>`
      
      `expr <BINARY | VARBINARY>, startExpr <BINARY | VARBINARY>`
      
      返回一个 `BOOLEAN`。如果任意参数为 `NULL`，则返回 `NULL`。
  - sql: ENDSWITH(expr, endExpr)
    table: expr.endsWith(endExpr)
    description: |
      判断 expr 是否以 endExpr 结尾。如果 endExpr 为空，则结果为 true。
      
      expr 和 endExpr 应具有相同的类型。
      
      `expr <CHAR | VARCHAR>, endExpr <CHAR | VARCHAR>`
      
      `expr <BINARY | VARBINARY>, endExpr <BINARY | VARBINARY>`
      
      返回一个 `BOOLEAN`。如果任意参数为 `NULL`，则返回 `NULL`。
  - sql: SUBSTRING(string FROM integer1 [ FOR integer2 ])
    table: |
      STRING.substring(INT1)
      STRING.substring(INT1, INT2)
    description: 返回 STRING 从位置 INT1 开始，长度为 INT2（默认到结尾）的子字符串。
  - sql: REPLACE(string1, string2, string3)
    table: STRING1.replace(STRING2, STRING3)
    description: |
      返回一个新字符串，它用 STRING3（非重叠）替换 STRING1 中所有出现的 STRING2。
      例如 `'hello world'.replace('world', 'flink')` 返回 `'hello flink'`；
      `'ababab'.replace('abab', 'z')` 返回 `'zab'`。
  - sql: REGEXP_COUNT(str, regex)
    table: str.regexpCount(regex)
    description: |
      返回字符串 str 匹配正则表达式模式 regex 的次数。regex 必须是一个 Java 正则表达式。
      
      `str <CHAR | VARCHAR>, regex <CHAR | VARCHAR>`
      
      返回一个 `INTEGER` 表示匹配成功的次数。如果任何参数为 `NULL` 或 regex 非法，则返回 `NULL`。
  - sql: REGEXP_EXTRACT(string1, string2[, integer])
    table: STRING1.regexpExtract(STRING2[, INTEGER1])
    description: |
      将字符串 STRING1 按照 STRING2 正则表达式的规则拆分，返回指定 INTEGER1 处位置的字符串。正则表达式匹配组索引从 1 开始，
      0 表示匹配整个正则表达式。此外，正则表达式匹配组索引不应超过定义的组数。
      例如 `REGEXP_EXTRACT('foothebar', 'foo(.*?)(bar)', 2)` 返回 `"bar"`。
  - sql: REGEXP_EXTRACT_ALL(str, regex[, extractIndex])
    table: str.regexpExtractAll(regex[, extractIndex])
    description: |
      提取字符串 str 中与正则表达式 regex 匹配且与 extractIndex 组对应的所有子串。
      
      regex 可以包含多个组。extractIndex 用于指示要提取哪个正则组，索引从 1 开始，也作为未指定时的默认值。0 表示匹配整个正则表达式。
      
      `str <CHAR | VARCHAR>, regex <CHAR | VARCHAR>, extractIndex <TINYINT | SMALLINT | INTEGER | BIGINT>`
      
      返回一个 `ARRAY<STRING>`，表示所有匹配的子串。如果任何参数为 `NULL`或非法，则返回 `NULL`。
  - sql: REGEXP_INSTR(str, regex)
    table: str.regexpInstr(regex)
    description: |
      返回 str 中第一个匹配 regex 的子字符串的索引。
      
      结果索引从 1 开始，如果匹配失败则返回 0。
      
      `str <CHAR | VARCHAR>, regex <CHAR | VARCHAR>`
      
      返回一个 `INTEGER` 表示 str 中第一个匹配 regex 的子字符串索引。如果任何参数为 `NULL` 或 regex 非法，则返回 `NULL`。
  - sql: REGEXP_SUBSTR(str, regex)
    table: str.regexpSubStr(regex)
    description: |
      返回 str 中第一个匹配 regex 的子字符串。
      
      `str <CHAR | VARCHAR>, regex <CHAR | VARCHAR>`
      
      返回一个 `STRING` 表示 str 中第一个匹配 regex 的子字符串。如果任何参数为 `NULL` 或 regex 非法或匹配失败，则返回 `NULL`。
  - sql: TRANSLATE(expr, fromStr, toStr)
    table: expr.translate(fromStr, toStr)
    description: |
      将 expr 中所有出现在 fromStr 之中的字符替换为 toStr 中的相应字符。如果 toStr 的长度短于 fromStr，则未匹配的字符将被移除。

      E.g., SELECT TRANSLATE3('www.apache.org', 'wapcheorg', ' APCHEcom') or 'www.apache.org'.translate('wapcheorg', ' APCHEcom') returns "   .APACHE.com".
      
      `expr <CHAR | VARCHAR>, fromStr <CHAR | VARCHAR>, toStr <CHAR | VARCHAR>`

      返回 `STRING` 格式的转换结果。
  - sql: INITCAP(string)
    table: STRING.initCap()
    description: |
      返回新形式的 STRING，其中每个单词的第一个字符转换为大写，其余字符转换为小写。这里的单词表示字母数字的字符序列。
  - sql: CONCAT(string1, string2, ...)
    table: concat(STRING1, STRING2, ...)
    description: |
      返回连接 string1，string2， ... 的字符串。如果有任一参数为 `NULL`，则返回 `NULL`。
      例如 `CONCAT('AA', 'BB', 'CC')` 返回 `"AABBCC"`。
  - sql: CONCAT_WS(string1, string2, string3, ...)
    table: concat_ws(STRING1, STRING2, STRING3, ...)
    description: |
      返回将 STRING2， STRING3， ... 与分隔符 STRING1 连接起来的字符串。在要连接的字符串之间添加分隔符。
      如果 STRING1 为 `NULL`，则返回 `NULL`。与 concat() 相比，concat_ws() 会自动跳过 `NULL` 参数。
      例如 `concat_ws('~', 'AA', Null(STRING), 'BB', '', 'CC')` 返回 `"AA~BB~~CC"`.
  - sql: LPAD(string1, integer, string2)
    table: STRING1.lpad(INT, STRING2)
    description: |
      返回从 string1 靠左填充 string2 到 integer 长度的新字符串。如果 string1 的长度小于 INT 值，则返回 string1 缩
      短到 integer 长度的字符串。例如 `LPAD('hi', 4, '??')` 返回 `"??hi"`；`LPAD('hi', 1, '??')` 返回 `"h"。
  - sql: RPAD(string1, integer, string2)
    table: STRING1.rpad(INT, STRING2)
    description: |
      返回从 string1 靠右边填充 string2 到 integer 长度的新字符串。如果 string1 的长度小于 integer 值，则返回 string1 缩
      短到 integer 长度的新字符串。例如 `RPAD('hi', 4, '??')` 返回 `"hi??"`, `RPAD('hi', 1, '??')` 返回 `"h"`。
  - sql: FROM_BASE64(string)
    table: STRING.fromBase64()
    description: |
      返回字符串 string 的 base64 解码的结果；如果字符串 string 为 `NULL`，则返回 `NULL`。
      例如 `FROM_BASE64('aGVsbG8gd29ybGQ=')` 返回 `"hello world"`。
  - sql: TO_BASE64(string)
    table: STRING.toBase64()
    description: |
      返回字符串 string 的 base64 编码的结果；如果字符串 string 为 `NULL`，则返回 `NULL`。
      例如 `TO_BASE64('hello world')` 返回 `"aGVsbG8gd29ybGQ="`。
  - sql: ASCII(string)
    table: STRING.ascii()
    description: |
      返回字符串 string 第一个字符的数值。如果字符串为 `NULL` 则返回 `NULL`。
      例如 `ascii('abc')` 返回 `97`，`ascii(CAST(NULL AS VARCHAR))` 返回 `NULL`。
  - sql: CHR(integer)
    table: INT.chr()
    description: |
      返回二进制等于 integer 的 ASCII 字符。如果整数 integer 大于 255，我们先将整数 integer 对 255 取模数，
      并返回模数的 CHR。如果整数为 `NULL`，则返回 `NULL`。例如 `chr(97)` 返回 `a`，`chr(353)` 返回 `a`，
      `ascii(CAST(NULL AS VARCHAR))` 返回 `NULL`。
  - sql: DECODE(binary, string)
    table: BINARY.decode(STRING)
    description: |
      使用提供的字符集 string（'US-ASCII'，'ISO-8859-1'，'UTF-8'，'UTF-16BE'，'UTF-16LE'，'UTF-16'）解码第一个参数 binary 为字符串。
      如果任一参数为空，则结果也将为空。
  - sql: ENCODE(string1, string2)
    table: STRING1.encode(STRING2)
    description: |
      使用提供的字符集 string2（'US-ASCII'，'ISO-8859-1'，'UTF-8'，'UTF-16BE'，'UTF-16LE'，'UTF-16'）将字符串 string1 编码。
      如果任一参数为空，则结果也将为空。
  - sql: INSTR(string1, string2)
    table: STRING1.instr(STRING2)
    description:  返回 string2 在 string1 中第一次出现的位置。如果有任一参数为 `NULL`，则返回 `NULL`。
  - sql: LEFT(string, integer)
    table: STRING.LEFT(INT)
    description: |
      返回字符串中最左边的长度为 integer 值的字符串。如果 integer 为负，则返回 `EMPTY` 字符串。如果有任一参数
      为 `NULL` 则返回 `NULL`。
  - sql: RIGHT(string, integer)
    table: STRING.RIGHT(INT)
    description: |
      返回字符串中最右边的长度为 integer 值的字符串。如果 integer 为负，则返回 `EMPTY` 字符串。如果有任一参数
      为 `NULL` 则返回 `NULL`。
  - sql: LOCATE(string1, string2[, integer])
    table: STRING1.locate(STRING2[, INTEGER])
    description: |
      返回 string2 中 string1 在位置 integer 之后第一次出现的位置。未找到返回 0。如果有任一参数为 `NULL` 则返回 `NULL`。
  - sql: URL_DECODE(string)
    table: STRING.urlDecode()
    description:
      使用 UTF-8 编码方案对“application/x-www-form-urlencoded”格式的给定字符串进行解码。
      如果输入为 NULL，或者解码过程出现问题（例如遇到非法转义模式或者不支持该编码方案），该函数将返回 NULL。
  - sql: URL_ENCODE(string)
    table: STRING.urlEncode()
    description:
      使用 UTF-8 编码方案将字符串转换为“application/x-www-form-urlencoded”格式。
      如果输入为 NULL，或者编码过程出现问题，或者不支持编码方案，则会返回 NULL。
  - sql: PARSE_URL(string1, string2[, string3])
    table: STRING1.parseUrl(STRING2[, STRING3])
    description: |
      从 URL 返回指定的部分。string2 的有效值包括“HOST”，“PATH”，“QUERY”，“REF”，“PROTOCOL”，“AUTHORITY”，“FILE”和“USERINFO”。
      如果有任一参数为 `NULL`，则返回 `NULL`。例如
      `parse_url('http://facebook.com/path1/p.php?k1=v1&k2=v2#Ref1', 'HOST')` 返回 `'facebook.com'`。
      还可以通过提供关键词 string3 作为第三个参数来提取 QUERY 中特定键的值。例如
      `parse_url('http://facebook.com/path1/p.php?k1=v1&k2=v2#Ref1', 'QUERY', 'k1')` 返回 `'v1'`。
  - sql: REGEXP(string1, string2)
    table: STRING1.regexp(STRING2)
    description: |
      如果 string1 的任何（可能为空）子字符串与 Java 正则表达式 string2 匹配，则返回 TRUE，否则返回 FALSE。
      如果有任一参数为 `NULL`，则返回 `NULL`。
  - sql: REVERSE(string)
    table: STRING.reverse()
    description: 返回反转的字符串。如果字符串为 `NULL`，则返回 `NULL`。
  - sql: SPLIT_INDEX(string1, string2, integer1)
    table: STRING1.splitIndex(STRING2, INTEGER1)
    description: |
      通过分隔符 string2 拆分 string1，返回分隔后这组字符串的第 integer（从0开始）个字符串。如果 integer 为负，则返回 `NULL`。
      如果有任一参数为 `NULL`，则返回 `NULL`。
  - sql: STR_TO_MAP(string1[, string2, string3])
    table: STRING1.strToMap([STRING2, STRING3])
    description: |
      使用分隔符将 string1 拆分为键值对后返回一个 map。string2 是 pair 分隔符，默认为 ','。string3 是键值分隔符，默认为 '='。
      pair 分隔符与键值分隔符均为正则表达式，当使用特殊字符作为分隔符时请提前进行转义，例如 `<([{\^-=$!|]})?*+.>`。
  - sql: SUBSTR(string, integer1[, integer2])
    table: STRING.substr(INTEGER1[, INTEGER2])
    description: 返回字符串的子字符串，从位置 integer1 开始，长度为 integer2（默认到末尾）。
  - sql: JSON_QUOTE(string)
    table: STRING.JsonQuote()
    description: Quotes a string as a JSON value by wrapping it with double quote characters, escaping interior quote and special characters ('"', '\', '/', 'b', 'f', 'n', 'r', 't'), and returning the result as a string. If the argument is NULL, the function returns NULL.
  - sql: JSON_UNQUOTE(string)
    table: STRING.JsonUnquote()
    description: Unquotes JSON value, unescapes escaped special characters ('"', '\', '/', 'b', 'f', 'n', 'r', 't', 'u' hex hex hex hex), and returns the result as a string. If the argument is NULL, returns NULL. If the value does not start and end with double quotes or if it starts and ends with double quotes but is not a valid JSON string literal, the value is passed through unmodified.
  - sql: ELT(index, expr[, exprs]*)
    table: index.elt(expr, exprs...)
    description: |
      返回第 index 个表达式。index 必须是介于 1 和 表达式数量之间的整数。
      
      E.g., SELECT ELT(2, 'scala-1', 'java-2', 'go-3') or 2.elt('scala-1', 'java-2', 'go-3') returns "java-2".
      
      index <TINYINT | SMALLINT | INTEGER | BIGINT>, expr <CHAR | VARCHAR>, exprs <CHAR | VARCHAR>
      
      index <TINYINT | SMALLINT | INTEGER | BIGINT>, expr <BINARY | VARBINARY>, exprs <BINARY | VARBINARY>
      
      结果类型为所有 expr 的公共类型。如果 index 为 `NULL` 或超出范围，则返回 `NULL`。

temporal:
  - sql: DATE string
    table: STRING.toDate()
    description: 以“yyyy-MM-dd”的形式返回从字符串解析的 SQL 日期。
  - sql: TIME string
    table: STRING.toTime()
    description: 以“HH:mm:ss”的形式返回从字符串解析的 SQL 时间。
  - sql: TIMESTAMP string
    table: STRING.toTimestamp()
    description: 以“yyyy-MM-dd HH:mm:ss[.SSS]”的形式返回从字符串解析的 SQL 时间戳。
  - sql: INTERVAL string range
    description: |
      从“dd hh:mm:ss.fff”形式的字符串解析 SQL 毫秒间隔或者从“yyyy-mm”形式的字符串解析 SQL 月数间隔。间隔范围可以
      是 DAY，MINUTE，DAY TO HOUR 或 DAY TO SECOND，以毫秒为间隔；YEAR 或 YEAR TO MONTH 表示几个月的间隔。例
      如 `INTERVAL '10 00:00:00.004' DAY TO SECOND，INTERVAL '10' DAY` 或 `INTERVAL '2-10' YEAR TO
      MONTH` 返回间隔。
  - table: NUMERIC.year | NUMERIC.years
    description: 创建 NUMERIC 年的月间隔。
  - table: NUMERIC.quarter | NUMERIC.quarters
    description: 为 NUMERIC 季度创建月间隔。例如 `2.quarters` 返回 `6`。
  - table: NUMERIC.month | NUMERIC.months
    description: 创建 NUMERIC 个月的间隔。
  - table: NUMERIC.week | NUMERIC.weeks
    description: 为 NUMERIC 周创建毫秒间隔。例如 2.weeks 返回 1209600000。
  - table: NUMERIC.day | NUMERIC.days
    description: 创建 NUMERIC 天的毫秒间隔。
  - table: NUMERIC.hour | NUMERIC.hours
    description: 创建 NUMERIC 小时的毫秒间隔。
  - table: NUMERIC.minute | NUMERIC.minutes
    description: 创建 NUMERIC 分钟的毫秒间隔。
  - table: NUMERIC.second | NUMERIC.seconds
    description: 创建 NUMERIC 秒的毫秒间隔。
  - table: NUMERIC.milli | NUMERIC.millis
    description: 创建 NUMERIC 毫秒的毫秒间隔。
  - sql: LOCALTIME
    table: localTime()
    description: |
      返回本地时区的当前 SQL 时间，返回类型为 TIME(0)。在流模式下为每条记录进行取值。
      但在批处理模式下，它在查询开始时计算一次，并对每一行使用相同的结果。
  - sql: LOCALTIMESTAMP
    table: localTimestamp()
    description: |
      返回本地时区的当前 SQL 时间戳，返回类型为 TIMESTAMP(3)。在流模式下为每条记录进行取值。
      但在批处理模式下，它在查询开始时计算一次，并对每一行使用相同的结果。
  - sql: CURRENT_TIME
    table: currentTime()
    description: 返回本地时区的当前 SQL 时间，这是 LOCAL_TIME 的同义词。
  - sql: CURRENT_DATE
    table: currentDate()
    description: |
      返回本地时区中的当前 SQL 日期。在流模式下为每条记录进行取值。
      但在批处理模式下，它在查询开始时计算一次，并对每一行使用相同的结果。
  - sql: CURRENT_TIMESTAMP
    table: currentTimestamp()
    description: |
      返回本地时区的当前 SQL 时间戳，返回类型为 TIMESTAMP_LTZ(3)。在流模式下为每条记录进行取值。
      但在批处理模式下，它在查询开始时计算一次，并对每一行使用相同的结果。
  - sql: NOW()
    description: 返回本地时区的当前 SQL 时间戳，这是 CURRENT_TIMESTAMP 的同义词。
  - sql: CURRENT_ROW_TIMESTAMP()
    description: |
      返回本地时区的当前 SQL 时间戳，返回类型为 TIMESTAMP_LTZ(3)。无论是在批处理模式还是流模式下，都会为每条记录进行取值。
  - sql: EXTRACT(timeinteravlunit FROM temporal)
    table: TEMPORAL.extract(TIMEINTERVALUNIT)
    description: 返回从时间 temporal 的时间间隔单位部分 timeinteravlunit 提取的 long 值。例如 `EXTRACT(DAY FROM DATE '2006-06-05')` 返回 5。
  - sql: YEAR(date)
    description: |
      从 SQL 日期 date 返回年份。相当于 EXTRACT(YEAR FROM date)。例如 `YEAR(DATE '1994-09-27')` 返回 1994。
  - sql: QUARTER(date)
    description: |
      从 SQL 日期 date 返回一年中的季度（1 到 4 之间的整数）。相当于 EXTRACT(QUARTER FROM date)。
      例如 `QUARTER(DATE '1994-09-27')` 返回 3。
  - sql: MONTH(date)
    description: |
      从 SQL 日期 date 返回一年中的月份（1 到 12 之间的整数）。相当于 EXTRACT(MONTH FROM date)。
      例如 `MONTH(DATE '1994-09-27')` 返回 9。
  - sql: WEEK(date)
    description: |
      从 SQL 日期 date 返回一年中的第几周（1 到 53 之间的整数）。相当于 EXTRACT(WEEK FROM date)。
      例如 `WEEK(DATE '1994-09-27')` 返回 39。
  - sql: DAYOFYEAR(date)
    description: |
      从 SQL 日期 date 返回一年中的第几天（1 到 366 之间的整数）。相当于 EXTRACT(DOY FROM date)。
      例如 `DAYOFYEAR(DATE '1994-09-27')` 返回 270。
  - sql: DAYOFMONTH(date)
    description: |
      从 SQL 日期 date 返回一个月中的第几天（1 到 31 之间的整数）。相当于 EXTRACT(DAY FROM date)。
      例如 `DAYOFMONTH(DATE '1994-09-27')` 返回 27。
  - sql: DAYOFWEEK(date)
    description: |
      从 SQL 日期 date 返回一个星期中的第几天（1 到 7 之间的整数）。相当于 EXTRACT(DOW FROM date)。
      例如 `DAYOFWEEK(DATE '1994-09-27')` 返回 3。
  - sql: HOUR(timestamp)
    description: |
      从 SQL 时间戳 timestamp 返回小时单位部分的小时（0 到 23 之间的整数）数。相当于 EXTRACT(HOUR FROM timestamp)。
      例如 `MINUTE(TIMESTAMP '1994-09-27 13:14:15')` 返回 14。
  - sql: MINUTE(timestamp)
    description: |
      从 SQL 时间戳 timestamp 返回分钟单位的分钟数（0 到 59 之间的整数）。相当于 EXTRACT(MINUTE FROM timestamp)。
      例如 `MINUTE(TIMESTAMP '1994-09-27 13:14:15')` 返回 14。
  - sql: SECOND(timestamp)
    description: |
      从 SQL 时间戳 timestamp 返回秒单位部分的秒数（0 到 59 之间的整数）。相当于 EXTRACT(SECOND FROM timestamp)。
      例如 `SECOND(TIMESTAMP '1994-09-27 13:14:15')` 返回 15。
  - sql: FLOOR(timepoint TO timeintervalunit)
    table: TIMEPOINT.floor(TIMEINTERVALUNIT)
    description: |
      返回将时间点 timepoint 向下取值到时间单位 timeintervalunit 的值。例如 `FLOOR(TIME '12:44:31' TO MINUTE)`
      返回 12:44:00。
  - sql: CEIL(timespoint TO timeintervaluntit)
    table: TIMEPOINT.ceil(TIMEINTERVALUNIT)
    description: |
      返回将时间点 timespoint 向上取值到时间单位 timeintervaluntit 的值。例如 `CEIL(TIME '12:44:31' TO MINUTE)`
      返回 12:45:00。
  - sql: (timepoint1, temporal1) OVERLAPS (timepoint2, temporal2)
    table: temporalOverlaps(TIMEPOINT1, TEMPORAL1, TIMEPOINT2, TEMPORAL2)
    description: |
      如果由 (timepoint1, temporal1) 和 (timepoint2, temporal2) 定义的两个时间间隔重叠，则返回 TRUE。
      时间值 temporal1 可以是时间点或时间间隔。例如
      `(TIME '2:55:00', INTERVAL '1' HOUR) OVERLAPS (TIME '3:30:00', INTERVAL '2' HOUR)` 返回 TRUE；
      `(TIME '9:00:00', TIME '10:00:00') OVERLAPS (TIME '10:15:00', INTERVAL '3' HOUR)` 返回 FALSE。
  - sql: DATE_FORMAT(timestamp, string)
    table: dateFormat(TIMESTAMP, STRING)
    description: |
      将时间戳 timestamp 转换为日期格式字符串 string 指定格式的字符串值。格式字符串与 Java 的 SimpleDateFormat 兼容。
  - sql: TIMESTAMPADD(timeintervalunit, interval, timepoint)
  - sql: TIMESTAMPDIFF(timepointunit, timepoint1, timepoint2)
    table: timestampDiff(TIMEPOINTUNIT, TIMEPOINT1, TIMEPOINT2)
    description: |
      返回 timepoint1 和 timepoint2 之间时间间隔。间隔的单位由第一个参数给出，它应该是以下值之一：
      SECOND，MINUTE，HOUR，DAY，MONTH 或 YEAR。
  - sql: CONVERT_TZ(string1, string2, string3)
    table: convertTz(STRING1, STRING2, STRING3)
    description: |
      将日期时间 string1（具有默认 ISO 时间戳格式 'yyyy-MM-dd HH:mm:ss'）从时区 string2 转换为时区 string3 的值。
      时区的格式应该是缩写如“PST”，全名如“America/Los_Angeles”，或自定义 ID 如“GMT-08:00”。例如
      `CONVERT_TZ('1970-01-01 00:00:00', 'UTC', 'America/Los_Angeles')` 返回 `'1969-12-31 16:00:00`'。
  - sql: FROM_UNIXTIME(numeric[, string])
    table: fromUnixtime(NUMERIC[, STRING])
    description: |
      以格式 string（默认为 'yyyy-MM-dd HH:mm:ss'）表示数字参数 numberic 的值。numeric 是一个内部
      时间戳值，表示自'1970-01-01 00:00:00' UTC 以来的秒数，由 UNIX_TIMESTAMP() 函数生成。返回值以会话时区表示
      （在 TableConfig 中指定）。例如，如果在 UTC 时区，FROM_UNIXTIME(44) 返回 '1970-01-01 00:00:44'，如果在
      'Asia/Tokyo' 时区，则返回 '1970-01-01 09:00:44'。
  - sql: UNIX_TIMESTAMP()
    table: unixTimestamp()
    description: 以秒为单位获取当前的 Unix 时间戳。此函数不是确定性的，这意味着将为每个记录重新计算该值。
  - sql: UNIX_TIMESTAMP(string1[, string2])
    table: unixTimestamp(STRING1[, STRING2])
    description: |
      使用表配置中指定的时区将格式为 string2 的日期时间字符串 string1（如果未指定默认情况下：yyyy-MM-dd HH:mm:ss）
      转换为 Unix 时间戳（以秒为单位）。
      
      如果时间戳字符串指定了时区并使用 UTC+X 的格式解析（例如："yyyy-MM-dd HH:mm:ss.SSS X"），此函数将会使用时间戳字符串中的时区来转换，而不是 Flink 会话中所配置的时区。
      如果时间戳字符串无法正常解析，此函数将会默认返回 Long.MIN_VALUE（即: -9223372036854775808）作为结果。

      ```sql
      Flink SQL> SET 'table.local-time-zone' = 'Europe/Berlin';

      -- 返回 25201
      Flink SQL> SELECT UNIX_TIMESTAMP('1970-01-01 08:00:01.001', 'yyyy-MM-dd HH:mm:ss.SSS');
      -- 返回 1
      Flink SQL> SELECT UNIX_TIMESTAMP('1970-01-01 08:00:01.001 +0800', 'yyyy-MM-dd HH:mm:ss.SSS X');
      -- 返回 25201
      Flink SQL> SELECT UNIX_TIMESTAMP('1970-01-01 08:00:01.001 +0800', 'yyyy-MM-dd HH:mm:ss.SSS');
      -- 返回 -9223372036854775808
      Flink SQL> SELECT UNIX_TIMESTAMP('1970-01-01 08:00:01.001', 'yyyy-MM-dd HH:mm:ss.SSS X');
      ```
  - sql: TO_DATE(string1[, string2])
    table: toDate(STRING1[, STRING2])
    description: 将格式为 string2（默认为 'yyyy-MM-dd'）的字符串 string1 转换为日期。
  - sql: TO_TIMESTAMP_LTZ(numeric[, precision])
    table: toTimestampLtz(NUMERIC, PRECISION)
    description: Converts an epoch seconds or epoch milliseconds to a TIMESTAMP_LTZ, the valid precision is 0 or 3, the 0 represents TO_TIMESTAMP_LTZ(epochSeconds, 0), the 3 represents TO_TIMESTAMP_LTZ(epochMilliseconds, 3). If no precision is provided, the default precision is 3. If any input is null, the function will return null.
  - sql: TO_TIMESTAMP_LTZ(string1[, string2[, string3]])
    table: toTimestampLtz(STRING1[, STRING2[, STRING3]])
    description: Converts a timestamp string string1 with format string2 (by default 'yyyy-MM-dd HH:mm:ss.SSS') in time zone string3 (by default 'UTC') to a TIMESTAMP_LTZ. If any input is null, the function will return null.
  - sql: TO_TIMESTAMP(string1[, string2])
    table: toTimestamp(STRING1[, STRING2])
    description: 将格式为 string2（默认为：'yyyy-MM-dd HH:mm:ss'）的字符串 string1 转换为 timestamp，不带时区。
  - sql: CURRENT_WATERMARK(rowtime)
    description: |
      返回给定时间列属性 rowtime 的当前水印，如果管道中的当前操作没有可用的上游操作的公共水印时则为 `NULL`。
      函数的返回类型被推断为与提供的时间列属性匹配，但调整后的精度为 3。例如时间列属性为 TIMESTAMP_LTZ(9)，则函数将返回
      TIMESTAMP_LTZ(3)。

      请注意，此函数可以返回 `NULL`，您可能必须考虑这种情况。例如，如果您想过滤掉迟到数据，您可以使用：
      ```sql
      WHERE
        CURRENT_WATERMARK(ts) IS NULL
        OR ts > CURRENT_WATERMARK(ts)
      ```

conditional:
  - sql: |
      CASE value
        WHEN value1_1 [, value1_2]* THEN RESULT1
        (WHEN value2_1 [, value2_2 ]* THEN result_2)*
        (ELSE result_z)
      END
    description: |
      当值 value 第一次满足在 (valueX_1, valueX_2, ...) 中时，返回对应的结果 resultX。当没有匹配到时，如果提供了 result_z 则返回 result_z，
      否则返回 `NULL`。
  - sql: |
      CASE
        WHEN condition1 THEN result1
        (WHEN condition2 THEN result2)*
        (ELSE result_z)
      END
    description: 满足第一个条件 X 时返回 resultX。当不满足任何条件时，如果提供了 result_z 则返回 result_z，否则返回 `NULL`。
  - sql: NULLIF(value1, value2)
    description: |
      如果 value1 等于 value2 返回 `NULL`；否则返回 value1。例如 `NULLIF(5, 5)` 返回 `NULL`；`NULLIF(5, 0)` 返回 5。
  - sql: COALESCE(value1, value2 [, value3]*)
    description: ｜
      从 value1, value2, ... 返回第一个不为 `NULL` 的值。例如 `COALESCE(3, 5, 3)` 返回 3。
      如果所有参数为 `NULL`，返回 `NULL`。返回类型是所有参数的限制最最少的通用类型，如果所有参数是可空的类型，返回类型也是可空的类型。
      
      ```sql
      -- 返回 'default'
      COALESCE(NULL, 'default')
      
      -- 返回 f0，f1中第一个非空的值，如果 f0 和 f1 都是 NULL，则返回 'default'
      COALESCE(f0, f1, 'default')
      ```
  - sql: IF(condition, true_value, false_value)
    description: 如果满足条件，返回 true_value，否则返回 false_value。例如 `IF(5 > 3, 5, 3)` 返回 5。
  - sql: IFNULL(input, null_replacement)
    table: input.ifNull(nullReplacement)
    description: |
      如果输入为 `NULL`，则返回 null_replacement；否则返回 input。
      
      与 COALESCE 或 CASE WHEN 相比，此函数返回的数据类型在是否为空方面非常明确。返回的类型是两个参数的公共类型，但只有在 null_replacement 可为空时才能为可空类型。
      该函数允许将可空的列传递到使用 NOT NULL 约束声明的函数或表中。
      
      例如 `IFNULL(nullable_column, 5)` 一定不返回 `NULL`。
  - sql: IS_ALPHA(string)
    description: 如果字符串中的所有字符都是字母，则返回 `true`，否则返回 `false`。
  - sql: IS_DECIMAL(string)
    description: 如果 string 可以解析为有效数字，则返回 `true`，否则返回 `false`。
  - sql: IS_DIGIT(string)
    description: 如果字符串中的所有字符都是数字，则返回 `true`，否则返回 `false`。
  - table: BOOLEAN.?(VALUE1, VALUE2)
    description: |
      如果 BOOLEAN 计算结果为 TRUE，则返回 VALUE1；否则返回 VALUE2。例如 `(42 > 5).?('A', 'B')` 返回 `"A"`。
  - sql: GREATEST(value1[, value2]*)
    description: 返回所有输入参数的最大值，如果输入参数中包含 NULL，则返回 NULL。
  - sql: LEAST(value1[, value2]*)
    description: 返回所有输入参数的最小值，如果输入参数中包含 NULL，则返回 NULL。

conversion:
  - sql: CAST(value AS type)
    table: ANY.cast(TYPE)
    description: |
      返回 value 被转换为类型 type 的新值。CAST错误会抛出异常并导致作业失败。为了处理错误，在使用可能失败的 CAST 操作时，例如 STRING 转换为 INT，建议使用 TRY_CAST 替代。
      如果开启了 "table.exec.legacy-cast-behaviour"，CAST 行为将变得与 TRY_CAST 一致。
      
      例如, CAST('42' AS INT) 返回 42; CAST(NULL AS STRING) 返回字符串类型的 `NULL`; CAST('non-number' AS INT) 抛出异常且作业失败。
  - sql: TRY_CAST(value AS type)
    table: ANY.tryCast(TYPE)
    description: |
      TRY_CAST 行为与 CAST 相似, 但在遇到错误时 TRY_CAST 会返回 NULL 而不是使作业失败。
      例如：TRY_CAST('42' AS INT) 返回 42; TRY_CAST(NULL AS STRING) 返回字符串类型的 `NULL`; TRY_CAST('non-number' AS INT) 返回 INT 类型的 `NULL`; COALESCE(TRY_CAST('non-number' AS INT), 0) 返回 INT 类型的 0。
  - sql: TYPEOF(input) | TYPEOF(input, force_serializable)
    table: call("TYPEOF", input) | call("TYPEOF", input, force_serializable)
    description: |
      返回输入表达式的数据类型的字符串表示。默认情况下返回的字符串是一个摘要字符串，可能会为了可读性而省略某些细节。
      如果 force_serializable 设置为 TRUE，则字符串表示可以持久化保存在 catalog 中的完整数据类型。
      请注意，特别是匿名的内联数据类型没有可序列化的字符串表示。在这种情况下返回 `NULL`。

collection:
  - sql: CARDINALITY(array)
    table: ARRAY.cardinality()
    description: 返回数组中元素的数量。
  - sql: array '[' INT ']'
    table: ARRAY.at(INT)
    description: 返回数组中 INT 位置的元素。索引从 1 开始。
  - sql: ELEMENT(array)
    table: ARRAY.element()
    description: 返回数组的唯一元素（其基数应为 1）；如果数组为空，则返回 `NULL`。如果数组有多个元素，则抛出异常。
  - sql: CARDINALITY(map)
    table: MAP.cardinality()
    description: 返回 map 中的 entries 数量。
  - sql: map '[' value ']'
    table: MAP.at(ANY)
    description: 返回 map 中指定 key 对应的值。
  - sql: ARRAY_APPEND(array, element)
    table: array.arrayAppend(element)
    description: Appends an element to the end of the array and returns the result. If the array itself is null, the function will return null. If an element to add is null, the null element will be added to the end of the array.
  - sql: ARRAY_CONTAINS(haystack, needle)
    table: haystack.arrayContains(needle)
    description: 返回是否数组 haystack 中包含指定元素 needle。支持检查数组中是否存在 null。 如果数组本身是null，函数会返回 null。如果需要，指定元素会隐式转换为数组的元素类型。
  - sql: ARRAY_DISTINCT(haystack)
    table: haystack.arrayDistinct()
    description: 返回没有重复元素的新数组。如果数组本身是null，函数会返回 null。函数会保留数组中元素的顺序。
  - sql: ARRAY_POSITION(haystack, needle)
    table: haystack.arrayPosition(needle)
    description: 返回数组中第一次出现 needle 元素的位置，返回类型为 int。如果数组中不存在该元素则返回 0。如果两个参数中任何一个参数为 null，则返回 null。序号不是从 0 开始，而是从 1 开始，第一个元素的序号为 1。
  - sql: ARRAY_PREPEND(array, element)
    table: array.arrayPrepend(element)
    description: Appends an element to the beginning of the array and returns the result. If the array itself is null, the function will return null. If an element to add is null, the null element will be added to the beginning of the array.
  - sql: ARRAY_REMOVE(haystack, needle)
    table: haystack.arrayRemove(needle)
    description: 删除数组中所有和元素 needle 相等的元素。如果数组是 null，则返回 null。函数会保留数组中元素的顺序。
  - sql: ARRAY_REVERSE(haystack)
    table: haystack.arrayReverse()
    description: 翻转并返回数组 haystack。 如果数组是 null，则返回 null。
  - sql: ARRAY_SLICE(array, start_offset[, end_offset])
    table: array.arraySlice(start_offset[, end_offset])
    description: 返回输入数组的子数组，位于“start_offset”和“end_offset”之间
      包含“start_offset”和“end_offset”）。偏移量从 1 开始，但 0 也被视为数组的开头。正值从数组开头开始计数，
      负值从数组末尾开始计数。如果省略“end_offset”，则该偏移量将被视为数组的长度。如果“start_offset”在
      “end_offset”之后或者两者都超出数组范围，则将返回空数组。如果任何输入为 null，则返回 null。
  - sql: ARRAY_UNION(array1, array2)
    table: haystack.arrayUnion(array)
    description: 返回由 array1 和 array2 的全部元素构成的数组，不包含重复元素。如果任意一个数组是 null，该函数返回 null。
  - sql: ARRAY_CONCAT(array1, ...)
    table: array1.arrayConcat(...)
    description: 返回一个数组，该数组是连接至少一个数组的结果。该数组包含第一个数组中的所有元素，然后是第二个数组中的所有元素，依此类推，直到第 N 个数组。如果任何输入数组为 NULL，则函数返回 NULL。
  - sql: ARRAY_EXCEPT(array1, array2)
    table: arrayOne.arrayExcept(arrayTwo)
    description: Returns an ARRAY that contains the elements from array1 that are not in array2, without duplicates. If no elements remain after excluding the elements in array2 from array1, the function returns an empty ARRAY. If one or both arguments are NULL, the function returns NULL. The order of the elements from array1 is kept.
  - sql: ARRAY_INTERSECT(array1, array2)
    table: array1.arrayIntersect(array2)
    description: Returns an ARRAY that contains the elements from array1 that are also in array2, without duplicates. If no elements that are both in array1 and array2, the function returns an empty ARRAY. If any of the array is null, the function will return null. The order of the elements from array1 is kept.
  - sql: ARRAY_MAX(array)
    table: array.arrayMax()
    description: 返回数组中的最大值，如果数组是 null，则返回 null。
  - sql: ARRAY_JOIN(array, delimiter[, nullReplacement])
    table: array.arrayJoin(delimiter[, nullReplacement])
    description: 返回一个字符串，表示给定数组中元素的串联，并且给定数组中元素的数据类型为字符串。 分隔符 delimiter 是一个字符串，用于分隔数组的每对连续元素。 可选的参数 nullReplacement 是一个字符串，用于替换数组中的 null 元素。 如果未指定 nullReplacement，则结果字符串中将从数组中省略 null 元素。 如果输入数组或分隔符或 nullReplacement 为 null，则返回 null。
  - sql: MAP_KEYS(map)
    table: MAP.mapKeys()
    description: 以数组形式返回 map 中的所有 key，不保证顺序。
  - sql: MAP_VALUES(map)
    table: MAP.mapValues()
    description: 以数组形式返回 map 中的所有 value，不保证顺序。
  - sql: MAP_ENTRIES(map)
    table: MAP.mapEntries()
    description: 以数组形式返回 map 中的所有 entry，不保证顺序。
  - sql: MAP_FROM_ARRAYS(array_of_keys, array_of_values)
    table: mapFromArrays(array_of_keys, array_of_values)
    description: 返回由 key 的数组 keys 和 value 的数组 values 创建的 map。请注意两个数组的长度应该相等。
  - sql: MAP_UNION(map1, map2)
    table: map1.mapUnion(map2)
    description: 返回一个通过合并两个图 'map1' 和 'map2' 创建的图。这两个图应该具有共同的图类型。如果有重叠的键，'map2' 的值将覆盖 'map1' 的值。如果任一图为空，则返回 null。
  - sql: SPLIT(string, delimiter)
    table: string.split(delimiter)
    description: Returns an array of substrings by splitting the input string based on the given delimiter. If the delimiter is not found in the string, the original string is returned as the only element in the array. If the delimiter is empty, every character in the string is split. If the string or delimiter is null, a null value is returned. If the delimiter is found at the beginning or end of the string, or there are contiguous delimiters, then an empty string is added to the array.

json:
  - sql: IS JSON [ { VALUE | SCALAR | ARRAY | OBJECT } ]
    table: STRING.isJson([JsonType type])
    description: |
      判定给定字符串是否为有效的 JSON。

      指定可选参数 type 将会限制 JSON 对象所允许的类型。
      如果字符串是有效的 JSON，但不是指定的类型，则返回 `false`。默认值为 `VALUE`。

      ```sql
      -- TRUE
      '1' IS JSON
      '[]' IS JSON
      '{}' IS JSON

      -- TRUE
      '"abc"' IS JSON
      -- FALSE
      'abc' IS JSON
      NULL IS JSON

      -- TRUE
      '1' IS JSON SCALAR
      -- FALSE
      '1' IS JSON ARRAY
      -- FALSE
      '1' IS JSON OBJECT

      -- FALSE
      '{}' IS JSON SCALAR
      -- FALSE
      '{}' IS JSON ARRAY
      -- TRUE
      '{}' IS JSON OBJECT
      ```
  - sql: JSON_EXISTS(jsonValue, path [ { TRUE | FALSE | UNKNOWN | ERROR } ON ERROR ])
    table: STRING.jsonExists(STRING path [, JsonExistsOnError onError])
    description: |
      判定 JSON 字符串是否满足给定的路径搜索条件。

      如果要忽略错误行为，那么将 `FALSE ON ERROR` 设为默认值。

      ```sql
      -- TRUE
      SELECT JSON_EXISTS('{"a": true}', '$.a');
      -- FALSE
      SELECT JSON_EXISTS('{"a": true}', '$.b');
      -- TRUE
      SELECT JSON_EXISTS('{"a": [{ "b": 1 }]}',
        '$.a[0].b');

      -- TRUE
      SELECT JSON_EXISTS('{"a": true}',
        'strict $.b' TRUE ON ERROR);
      -- FALSE
      SELECT JSON_EXISTS('{"a": true}',
        'strict $.b' FALSE ON ERROR);
      ```
  - sql: JSON_STRING(value)
    table: jsonString(value)
    description: |
      将值序列化为 JSON。

      此函数返回一个包含序列化值的 JSON 字符串。如果值为 `NULL`，函数返回 `NULL`。

      ```sql
      -- NULL
      JSON_STRING(CAST(NULL AS INT))

      -- '1'
      JSON_STRING(1)
      -- 'true'
      JSON_STRING(TRUE)
      -- '"Hello, World!"'
      JSON_STRING('Hello, World!')
      -- '[1,2]'
      JSON_STRING(ARRAY[1, 2])
      ```
  - sql: JSON_VALUE(jsonValue, path [RETURNING <dataType>] [ { NULL | ERROR | DEFAULT <defaultExpr> } ON EMPTY ] [ { NULL | ERROR | DEFAULT <defaultExpr> } ON ERROR ])
    table: STRING.jsonValue(STRING path [, returnType, onEmpty, defaultOnEmpty, onError, defaultOnError])
    description: |
      从 JSON 字符串中提取标量。

      此方法搜索给定路径表达式的 JSON 字符串，如果该路径上的值是标量则返回该值。不能返回非标量值。默认情况下返回值类型为 `STRING`。
      可以将 `dataType` 设置不同的类型，支持的类型如下所示:

      * `VARCHAR` / `STRING`
      * `BOOLEAN`
      * `INTEGER`
      * `DOUBLE`

      对于空路径表达式或错误，可以将行为定义为返回 `null`、引发错误或返回定义的默认值。当省略时，默认为 `NULL ON EMPTY` 或
      `NULL ON ERROR`。默认值可以是文字或表达式。如果默认值本身引发错误，就会造成 `ON EMPTY` 的错误行为，并引发 `ON ERROR` 的错误。
      
      对于路径表达式中包含特殊字符（如空格），你可以使用`['property']` 或 `["property"]`来引用父对象中指定的属性。请确保在属性名两侧加上单引号或双引号。
      当在 SQL 使用 JSON_VALUE 时，路径作为一个字符串参数已经被单引号引用了，因此你必须将属性名上的单引号转义，如`JSON_VALUE('{"a b": "true"}', '$.[''a b'']')`。
      
      ```sql
      -- "true"
      JSON_VALUE('{"a": true}', '$.a')

      -- TRUE
      JSON_VALUE('{"a": true}', '$.a' RETURNING BOOLEAN)

      -- "false"
      JSON_VALUE('{"a": true}', 'lax $.b'
          DEFAULT FALSE ON EMPTY)

      -- "false"
      JSON_VALUE('{"a": true}', 'strict $.b'
          DEFAULT FALSE ON ERROR)

      -- 0.998D
      JSON_VALUE('{"a.b": [0.998,0.996]}','$.["a.b"][0]'
          RETURNING DOUBLE)
      
      -- "right"
      JSON_VALUE('{"contains blank": "right"}', 'strict $.[''contains blank'']' NULL ON EMPTY DEFAULT 'wrong' ON ERROR)
      ```
  - sql: JSON_QUERY(jsonValue, path [ { WITHOUT | WITH CONDITIONAL | WITH UNCONDITIONAL } [ ARRAY ] WRAPPER ] [ { NULL | EMPTY ARRAY | EMPTY OBJECT | ERROR } ON EMPTY ] [ { NULL | EMPTY ARRAY | EMPTY OBJECT | ERROR } ON ERROR ])
    table: STRING.jsonQuery(path [, JsonQueryWrapper [, JsonQueryOnEmptyOrError, JsonQueryOnEmptyOrError ] ])
    description: |
      从 JSON 字符串中提取 JSON 值。

      结果总是以 `STRING` 的形式返回。目前尚不支持 `RETURNING` 子句。

      `wrappingBehavior` 决定是否将提取的值包装到一个数组中，以及是否无条件地这样做，还是只有当值本身不是数组时才这样做。

      `onEmpty` 和 `onError` 分别决定路径表达式为空或引发错误时的行为。默认情况下，这两种情况都会返回 `null`。其他选择是
      使用空数组、空对象或引发错误。

      ```sql
      -- '{ "b": 1 }'
      JSON_QUERY('{ "a": { "b": 1 } }', '$.a')
      -- '[1, 2]'
      JSON_QUERY('[1, 2]', '$')
      -- NULL
      JSON_QUERY(CAST(NULL AS STRING), '$')
      -- '["c1","c2"]'
      JSON_QUERY('{"a":[{"c":"c1"},{"c":"c2"}]}',
          'lax $.a[*].c')

      -- 将结果包装到数组中
      -- '[{}]'
      JSON_QUERY('{}', '$' WITH CONDITIONAL ARRAY WRAPPER)
      -- '[1, 2]'
      JSON_QUERY('[1, 2]', '$' WITH CONDITIONAL ARRAY WRAPPER)
      -- '[[1, 2]]'
      JSON_QUERY('[1, 2]', '$' WITH UNCONDITIONAL ARRAY WRAPPER)

      -- 必须包装标量才能返回
      -- NULL
      JSON_QUERY(1, '$')
      -- '[1]'
      JSON_QUERY(1, '$' WITH CONDITIONAL ARRAY WRAPPER)

      -- 路径表达式为空/存在错误时的行为
      -- '{}'
      JSON_QUERY('{}', 'lax $.invalid' EMPTY OBJECT ON EMPTY)
      -- '[]'
      JSON_QUERY('{}', 'strict $.invalid' EMPTY ARRAY ON ERROR)
      ```
  - sql: JSON_OBJECT([[KEY] key VALUE value]* [ { NULL | ABSENT } ON NULL ])
    table: jsonObject(JsonOnNull, keyValues...)
    description: |
      将键值对列表构建为 JSON 对象字符串。

      注意，键必须是非 `NULL` 字符串字面值，而值可以是任意表达式。

      这个函数返回一个 JSON 字符串。`ON NULL` 行为定义了如何处理 `NULL` 值。如果省略，则默认为 `NULL ON NULL`。

      值是由另一个 JSON 构造函数调用 (`JSON_OBJECT`，`JSON_ARRAY`) 直接插入所创建，而不是作为一个字符串。它允许构建嵌套的 JSON 结构。

      ```sql
      -- '{}'
      JSON_OBJECT()

      -- '{"K1":"V1","K2":"V2"}'
      JSON_OBJECT('K1' VALUE 'V1', 'K2' VALUE 'V2')

      -- 表达式作为值
      JSON_OBJECT('orderNo' VALUE orders.orderId)

      -- ON NULL
      JSON_OBJECT(KEY 'K1' VALUE CAST(NULL AS STRING) NULL ON NULL)   -- '{"K1":null}'
      JSON_OBJECT(KEY 'K1' VALUE CAST(NULL AS STRING) ABSENT ON NULL) -- '{}'

      -- '{"K1":{"K2":{"value":42}}}'
      JSON_OBJECT('K1' VALUE JSON('{"K2": {"value": 42}}'))
      
      -- '{"K1":{"K2":"V"}}'
      JSON_OBJECT(
        KEY 'K1'
        VALUE JSON_OBJECT(
          KEY 'K2'
          VALUE 'V'
        )
      )
      ```
      - sql: JSON(value)
      table: json(value)  
        description: |
          需要的参数是一个原始的、预先格式化的 JSON 字符串，返回值是这个字符串对应的JSON值，而不将其作为字符串转义。
          该函数目前只能在“JSON_OBJECT”函数中使用。它允许通过预格式化的 JSON 字符串将直接插入到生成的 JSON 结构中，而不是作为字符串值转义。
          该函数通常用来读取外部的Json字符串，将其转成 JSON 结构存储在JSON_OBJECT。如果值为 NULL 或空，则该函数返回 NULL。
          ```sql
          -- {"K":{"K2":42}}
          JSON_OBJECT('K' VALUE JSON('{"K2": 42}'))
          -- {"K":{"K2":{"K3":42}}}
          JSON_OBJECT('K' VALUE JSON('{"K2":{"K3":42}}'))
          -- {"K": null}
          JSON_OBJECT('K' VALUE JSON(''))
          -- JSON 函数只能在 JSON_OBJECT 函数中使用
          JSON('{"value": 42}')
          ```
  - sql: JSON_ARRAY([value]* [ { NULL | ABSENT } ON NULL ])
    table: jsonArray(JsonOnNull, values...)
    description: |
      将数值列表构建为 JSON 数组字符串。

      这个函数返回一个 JSON 字符串，值可以是任意表达式。`ON NULL` 行为定义了如何处理 `NULL` 值。如果省略，则假定 `ABSENT ON NULL` 为默认值。

      元素是由另一个 JSON 构造函数调用 (`JSON_OBJECT`，`JSON_ARRAY`) 直接插入所创建，而不是作为一个字符串。它允许构建嵌套的 JSON 结构。

      ```sql
      -- '[]'
      JSON_ARRAY()
      -- '[1,"2"]'
      JSON_ARRAY(1, '2')

      -- 表达式作为值
      JSON_ARRAY(orders.orderId)

      -- ON NULL
      JSON_ARRAY(CAST(NULL AS STRING) NULL ON NULL) -- '[null]'
      JSON_ARRAY(CAST(NULL AS STRING) ABSENT ON NULL) -- '[]'

      -- '[[1]]'
      JSON_ARRAY(JSON_ARRAY(1))
      ```

valueconstruction:
  - sql: |
      **implicit** constructor with parenthesis 
      
      (value1 [, value2]*)

      **explicit** ROW constructor with 
      
      ROW(value1 [, value2]*)
    table: row(ANY1, ANY2, ...)
    description: |
      返回从值列表 (value1, value2, ...) 创建的行。隐式行构造函数至少需要两个字段。显式行构造函数可以处理任意数量的字段。
      两者都支持任意表达式作为字段
  - sql: ARRAY '[' value1 [, value2 ]* ']'
    table: array(ANY1, ANY2, ...)
    description: 返回从值列表 (value1, value2, ...) 创建的数组。
  - sql: MAP '[' value1, value2 [, value3, value4 ]* ']'
    table: map(ANY1, ANY2, ANY3, ANY4, ...)
    description: 返回从键值对列表 ((value1, value2), (value3, value4), ...) 创建的 map。
  - sql: DESCRIPTOR '(' identifier1 [, identifier2  ]* ')'
    table: descriptor(STRING1, STRING2, ...)
    description: |
      Returns a literal describing an arbitrary, unvalidated list of column names. Passing a list of columns
      can be useful for parameterizing a function. In particular, it enables declaring the `on_time` argument
      for process table functions (PTFs).
      ```sql
      f(columns => DESCRIPTOR(`col1`, `col2`), on_time => DESCRIPTOR(`ts`))
      ```
  - table: NUMERIC.rows
    description: 创建一个 NUMERIC 行间隔（通常用于窗口创建）。

valueaccess:
  - sql: tableName.compositeType.field
    table: |
      COMPOSITE.get(STRING) 
      COMPOSITE.get(INT)
    description: 按名称从 Flink 复合类型（例如，Tuple，POJO）返回字段的值。
  - sql: tableName.compositeType.*
    table: ANY.flatten()
    description: |
      返回 Flink 复合类型（例如，Tuple，POJO）的平面表示，将其每个直接子类型转换为单独的字段。在大多数情况下，平面表示
      的字段与原始字段的命名类似，但使用 `$` 分隔符（例如 mypojo$mytuple$f0）。

grouping:
  - sql: GROUP_ID()
    description: 返回唯一标识分组键组合的整数。
  - sql: |
      GROUPING(expression1 [, expression2]* )
      GROUPING_ID(expression1 [, expression2]* )
    description: 返回给定分组表达式的位向量。

hashfunctions:
  - sql: MD5(string)
    table: STRING.md5()
    description: 以 32 位十六进制数字的字符串形式返回 string 的 MD5 哈希值；如果字符串为 `NULL`，则返回 `NULL`。
  - sql: SHA1(string)
    table: STRING.sha1()
    description: 以 40 位十六进制数字的字符串形式返回 string 的 SHA-1 哈希值；如果字符串为 `NULL`，则返回 `NULL`。
  - sql: SHA224(string)
    table: STRING.sha224()
    description: 以 56 位十六进制数字的字符串形式返回 string 的 SHA-224 哈希值；如果字符串为 `NULL`，则返回 `NULL`。
  - sql: SHA256(string)
    table: STRING.sha256()
    description: 以 64 位十六进制数字的字符串形式返回 string 的 SHA-256 哈希值；如果字符串为 `NULL`，则返回 `NULL`。
  - sql: SHA384(string)
    table: STRING.sha384()
    description: 以 96 位十六进制数字的字符串形式返回 string 的 SHA-384 哈希值；如果字符串为 `NULL`，则返回 `NULL`。
  - sql: SHA512(string)
    table: STRING.sha512()
    description: 以 128 位十六进制数字的字符串形式返回 string 的 SHA-512 哈希值；如果字符串为 `NULL`，则返回 `NULL`。
  - sql: SHA2(string, hashLength)
    table: STRING.sha2(INT)
    description: |
      使用 SHA-2 系列散列函数（SHA-224，SHA-256，SHA-384 或 SHA-512）返回散列值。第一个参数 string 是要散列的字符串，
      第二个参数 hashLength 是结果的位数（224，256，384 或 512）。如果 string 或 hashLength 为 `NULL`，则返回 `NULL`。

auxilary:
  - table: callSql(STRING)
    description: |
      对 SQL 表达式的调用。
      
      给定的字符串在规划期间被解析并转换为 Table API 表达式。只在运行时计算翻译后的表达式。
      目前，调用仅限于简单的标量表达式。不支持调用聚合函数或表值函数。子查询也是不允许的。
      例如 `table.select(callSql("UPPER(myColumn)").substring(3))`
  - table: ANY.as(NAME1, NAME2, ...)
    description: 指定 ANY（字段）的名称。如果表达式扩展为多个字段，则可以指定额外的名称。

aggregate:
  - sql: COUNT([ ALL ] expression | DISTINCT expression1 [, expression2]*)
    description: 默认情况下或使用关键字 `ALL`，返回不为 `NULL` 的表达式的输入行数。使用 `DISTINCT` 则对所有值去重后计算。
  - sql: COUNT(*) | COUNT(1)
    table: FIELD.count
    description: 返回输入行数。
  - sql: AVG([ ALL | DISTINCT ] expression)
    table: FIELD.avg
    description: |
      默认情况下或使用关键字 `ALL`，返回所有输入行中表达式的平均值（算术平均值）。使用 `DISTINCT` 则对所有值去重后计算。
  - sql: SUM([ ALL | DISTINCT ] expression)
    table: FIELD.sum
    description: 默认情况下或使用关键字 `ALL`，返回所有输入行的表达式总和。使用 `DISTINCT` 则对所有值去重后计算。
  - table: FIELD.sum0
    description: 返回所有输入行的数字字段 FIELD 的总和。如果所有值都为 `NULL`，则返回 0。
  - sql: MAX([ ALL | DISTINCT ] expression)
    table: FIELD.max
    description: 默认情况下或使用关键字 `ALL`，返回所有输入行中表达式的最大值。使用 `DISTINCT` 则对所有值去重后计算。
  - sql: MIN([ ALL | DISTINCT ] expression )
    table: FIELD.min
    description: 默认情况下或使用关键字 `ALL`，返回所有输入行中表达式的最小值。使用 `DISTINCT` 则对所有值去重后计算。
  - sql: STDDEV_POP([ ALL | DISTINCT ] expression)
    table: FIELD.stddevPop
    description: 默认情况下或使用关键字 `ALL`，返回所有输入行中表达式的总体标准偏差。使用 `DISTINCT` 则对所有值去重后计算。
  - sql: STDDEV_SAMP([ ALL | DISTINCT ] expression)
    table: FIELD.stddevSamp
    description: 默认情况下或使用关键字 `ALL`，返回所有输入行中表达式的样本标准偏差。使用 `DISTINCT` 则对所有值去重后计算。
  - sql: VAR_POP([ ALL | DISTINCT ] expression)
    table: FIELD.varPop
    description: |
      默认情况下或使用关键字 `ALL`，返回所有输入行中表达式的总体方差（总体标准差的平方）。使用 `DISTINCT` 则对所有值去重后计算。
  - sql: VAR_SAMP([ ALL | DISTINCT ] expression)
    table: FIELD.varSamp
    description: |
      默认情况下或使用关键字 `ALL`，返回所有输入行中表达式的样本方差（样本标准差的平方）。使用 `DISTINCT` 则对所有值去重后计算。
  - sql: COLLECT([ ALL | DISTINCT ] expression)
    table: FIELD.collect
    description: |
      默认情况下或使用关键字 `ALL`，返回跨所有输入行的多组表达式。`NULL` 值将被忽略。使用 `DISTINCT` 则对所有值去重后计算。
  - sql: VARIANCE([ ALL | DISTINCT ] expression)
    description: VAR_SAMP() 的同义方法。
  - sql: RANK()
    description: 返回值在一组值中的排名。结果是 1 加上分区顺序中当前行之前或等于当前行的行数。排名在序列中不一定连续。
  - sql: DENSE_RANK()
    description: |
      返回值在一组值中的排名。结果是一加先前分配的等级值。与函数 rank 不同，dense_rank 不会在排名序列中产生间隙。
  - sql: ROW_NUMBER()
    description: |
      在窗口分区内根据 rows 的排序为每一行分配一个唯一的序列号，从 1 开始。ROW_NUMBER 和 RANK 相似。ROW_NUMBER 按
      顺序对所有行进行编号（例如 1，2，3，4，5）。RANK 为等值 row 提供相同的序列值（例如 1，2，2，4，5）。
  - sql: LEAD(expression [, offset] [, default])
    table: lead(expression [, offset] [, default])
    description: 返回窗口中当前行之后第 offset 行处的表达式值。offset 的默认值为 1，default 的默认值为 `NULL`。
  - sql: LAG(expression [, offset] [, default])
    table: lag(expression [, offset] [, default])
    description: 返回窗口中当前行之前第 offset 行处的表达式值。offset 的默认值为 1，default 的默认值为 `NULL`。
  - sql: FIRST_VALUE(expression)
    table: FIELD.firstValue
    description: 返回一组有序值中的第一个值。
  - sql: LAST_VALUE(expression)
    table: FIELD.lastValue
    description: 返回一组有序值中的最后一个值。
  - sql: LISTAGG(expression [, separator])
    table: FIELD.listagg
    description: 连接字符串表达式的值并在它们之间放置分隔符值。字符串末尾不添加分隔符。分隔符的默认值为“,”。
  - sql: CUME_DIST()
    description: 返回值在一组值的累积分布。结果是小于或等于当前行的值的行数除以窗口分区的总行数。
  - sql: PERCENT_RANK()
    description: 返回值在一组值的百分比排名。结果是当前行在窗口分区中的排名减 1，然后除以窗口分区的总行数减 1。如果窗口分区的总行数为 1，则该函数返回 0。
  - sql: NTILE(n)
    description: |
      将窗口分区中的所有数据按照顺序划分为 n 个分组，返回分配给各行数据的分组编号（从 1 开始，最大为 n）。
      如果不能均匀划分为 n 个分组，则剩余值从第 1 个分组开始，为每一分组分配一个。
      比如某个窗口分区有 6 行数据，划分为 4 个分组，则各行的分组编号为：1，1，2，2，3，4。
  - sql: ARRAY_AGG([ ALL | DISTINCT ] expression [ RESPECT NULLS | IGNORE NULLS ])
    table: FIELD.arrayAgg
    description: |
      默认情况下或使用关键字ALL，返回输入行中表达式所组成的数组，并且如果没有输入行，则返回 `NULL`。使用 `DISTINCT` 则对所有值去重后计算。
      默认情况下`NULL` 值不会被忽略，使用 `IGNORE NULLS` 忽略 `NULL` 值。
      目前尚不支持 `ORDER BY` 子句。
  - sql: JSON_OBJECTAGG([KEY] key VALUE value [ { NULL | ABSENT } ON NULL ])
    table: jsonObjectAgg(JsonOnNull, keyExpression, valueExpression)
    description: |
      通过将 key-value 聚合到单个 JSON 对象中，构建 JSON 对象字符串。

      键表达式必须返回不为空的字符串。值表达式可以是任意的，包括其他 JSON 函数。
      如果值为 `NULL`，则 `ON NULL` 行为定义了要执行的操作。如果省略，默认情况下假定为 `NULL ON NULL`。

      请注意，键必须是唯一的。如果一个键出现多次，将抛出一个错误。

      目前在 `OVER` windows 中不支持此函数。

      ```sql
      -- '{"Apple":2,"Banana":17,"Orange":0}'
      SELECT
        JSON_OBJECTAGG(KEY product VALUE cnt)
      FROM orders
      ```
  - sql: JSON_ARRAYAGG(items [ { NULL | ABSENT } ON NULL ])
    table: jsonArrayAgg(JsonOnNull, itemExpression)
    description: |
      通过将字段聚合到数组中构建 JSON 对象字符串。

      项目表达式可以是任意的，包括其他 JSON 函数。如果值为 `NULL`，则 `ON NULL` 行为定义了要执行的操作。如果省略，默认情况下假定为 `ABSENT ON NULL`。

      此函数目前不支持 `OVER` windows、未绑定的 session windows 或 hop windows。

      ```sql
      -- '["Apple","Banana","Orange"]'
      SELECT
        JSON_ARRAYAGG(product)
      FROM orders
      ```

catalog:
  - sql: CURRENT_DATABASE()
    table: currentDatabase()
    description: 返回当前数据库。
