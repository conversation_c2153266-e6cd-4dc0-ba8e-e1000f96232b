<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="IssueNavigationConfiguration">
    <option name="links">
      <list>
        <IssueNavigationLink>
          <option name="issueRegexp" value="FLINK\-\d+" />
          <option name="linkRegexp" value="https://issues.apache.org/jira/browse/$0" />
        </IssueNavigationLink>
        <IssueNavigationLink>
          <option name="issueRegexp" value="FLIP\-\d+" />
          <option name="linkRegexp" value="https://cwiki.apache.org/confluence/display/FLINK/$0" />
        </IssueNavigationLink>
        <IssueNavigationLink>
          <option name="issueRegexp" value="#(\d+)" />
          <option name="linkRegexp" value="https://github.com/apache/flink/pull/$1" />
        </IssueNavigationLink>
      </list>
    </option>
  </component>
  <component name="VcsDirectoryMappings">
    <mapping directory="$PROJECT_DIR$" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/docs/themes/book" vcs="Git" />
  </component>
</project>
